package model

import (
	"time"
)

// EndpointCompeteBrand 终端竞争对手品牌模型
type EndpointCompeteBrand struct {
	ID               int       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Endpoint         int       `gorm:"not null;column:endpoint" json:"endpoint"`
	Brand            string    `gorm:"type:varchar(255);not null;column:brand" json:"brand" comment:"竞争对手的品牌"`
	PositionPriority *string   `gorm:"type:varchar(50);column:position_priority" json:"position_priority,omitempty" comment:"所占位置(比如商场的位置,1号位,2号位这些)"`
	Pics             *string   `gorm:"type:text;column:pics" json:"pics,omitempty" comment:"竞争品牌的终端图片"`
	CreatedAt        time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	UpdatedAt        time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (EndpointCompeteBrand) TableName() string {
	return "endpoint_compete_brand"
}
