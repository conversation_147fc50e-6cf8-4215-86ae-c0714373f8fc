package endpoint

import (
	"marketing-app/internal/api/client/salesstat"
	salesstatConfig "marketing-app/internal/api/client/salesstat/config"
	handler "marketing-app/internal/handler/endpoint"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	service "marketing-app/internal/service/endpoint"

	"github.com/gin-gonic/gin"
)

type EndpointRouter struct {
	endpointHandler handler.Endpoint
}

func NewEndpointRouter() *EndpointRouter {
	database, _ := db.GetDB()
	repo := endpointRepo.NewEndpoint(database)
	adminUserRepo := repository.NewAdminUserRepository(database)

	// 创建销售统计API客户端配置
	salesStatConfig := salesstatConfig.LoadSalesStatConfig()
	salesStatClient := salesstat.NewSalesStatClient(salesStatConfig)

	endpointSvc := service.NewEndpointService(repo, adminUserRepo, salesStatClient)

	return &EndpointRouter{
		endpointHandler: handler.NewEndpointHandler(endpointSvc),
	}
}

func (e *EndpointRouter) Register(r *gin.RouterGroup) {
	g := r.Group("")
	{
		g.GET("/endpoints", e.endpointHandler.GetEndpoints)                  // 获取终端列表
		g.GET("/info_username", e.endpointHandler.GetEndpointInfoByUsername) // 根据用户名获取终端信息
		g.GET("/options", e.endpointHandler.GetEndpointOptions)              // 获取终端选项
		g.GET("/image", e.endpointHandler.GetEndpointImage)                  // 获取终端形象
		g.GET("/newimage", e.endpointHandler.GetEndpointNewImage)            // 获取终端形象变更
		g.POST("/newimage", e.endpointHandler.PostEndpointNewImage)          // 保存终端形象变更
		g.GET("/notice", e.endpointHandler.GetEndpointNotice)                // 获取终端公告
		g.GET("/notice_by_id", e.endpointHandler.GetEndpointNoticeById)      // 根据ID获取终端公告
		g.GET("/stat", e.endpointHandler.GetEndpointStat)                    // 获取终端统计
		g.GET("/detail", e.endpointHandler.GetEndpointDetail)                // 获取终端详情
		g.GET("/sales_stat", e.endpointHandler.GetSalesStat)                 // 获取销售统计
		g.GET("/region", e.endpointHandler.GetEndpointRegion)                // 获取终端区域
	}
}
