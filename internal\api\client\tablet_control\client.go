package tablet_control

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"marketing-app/internal/pkg/log"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	ParentAPIHost      = "http://api-parent.readboy.com"
	ParentAPIAppSecret = "2aeb3869d9b8ed736d6c572b08db1851"
	ParentAPIAppID     = "aide.readboy.com"
	HTTPTimeout        = 30 * time.Second

	ParentAdminHost      = "https://parentadmin.readboy.com"
	ParentAdminAppID     = "parentsadmin"
	ParentAdminAppSecret = "9b332c2653ce7189da101dac5a63fd4e"

	WatchAPIHost      = "http://api-wear.readboy.com"
	WatchAPIAppID     = "yx.readboy.com"
	WatchAPIAppSecret = "08d9543a320bbf64fe49e9acdf64a04f"

	SuperAPIHost      = "https://api-super.readboy.com"
	SuperAPIAppSecret = "7770ded7af9f48288fe207aee1d7f91f"
	SuperAPIAppID     = "terminal_server"
)

type Client struct {
	httpClient *http.Client
}

func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: HTTPTimeout,
		},
	}
}

// AllowUnknownApp 设置允许未知应用安装状态
func (c *Client) AllowUnknownApp(ctx *gin.Context, imei string, endpointID int, status int) (bool, string) {
	apiURL := ParentAPIHost + "/api/allow_unknown_app/set"

	// 构建请求参数
	params := url.Values{}
	params.Set("imei", imei)
	params.Set("tid", strconv.Itoa(endpointID))
	params.Set("status", strconv.Itoa(status))
	params.Set("sn", generateCommonAPISign(ParentAPIAppSecret, ParentAPIAppID))

	// 创建POST请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, bytes.NewBufferString(params.Encode()))
	if err != nil {
		return false, "创建请求失败"
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false, "系统错误"
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "系统错误"
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return false, "系统错误"
	}
	log.Info("result", zap.Any("result", result))

	// 检查响应状态
	if status, ok := result["status"].(float64); ok {
		if status == 1 {
			return true, "success"
		} else if status == 0 {
			if msg, ok := result["msg"].(string); ok {
				return false, msg
			}
		}
	}

	return false, "系统错误"
}

// GetAllowNsfwUrl 获取设备NSFW URL允许状态
func (c *Client) GetAllowNsfwUrl(ctx *gin.Context, imei string) (interface{}, string) {
	apiURL := SuperAPIHost + "/api/allow_nsfw_url/get"

	// 构建请求参数
	params := url.Values{}
	params.Set("imei", imei)
	params.Set("sn", generateCommonAPISign(SuperAPIAppSecret, SuperAPIAppID))
	params.Set("ua", "1///"+SuperAPIAppID+"//")

	// 创建GET请求
	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fullURL, nil)
	if err != nil {
		return nil, "创建请求失败"
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, "系统错误"
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "系统错误"
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, "系统错误"
	}
	log.Info("result", zap.Any("result", result))

	// 检查响应状态
	if errno, ok := result["errno"].(float64); ok {
		if errno == 0 {
			if data, exists := result["data"]; exists {
				return data, "success"
			}
			return nil, "success"
		} else {
			if errmsg, exists := result["errmsg"].(string); exists {
				return nil, errmsg
			}
		}
	}

	return nil, "系统错误"
}

// SetAllowNsfwUrl 设置设备NSFW URL允许状态
func (c *Client) SetAllowNsfwUrl(ctx *gin.Context, imei string, endpointID int, status int) (bool, string) {
	apiURL := SuperAPIHost + "/api/allow_nsfw_url/set"

	// 构建请求参数
	params := url.Values{}
	params.Set("imei", imei)
	params.Set("terminal_id", strconv.Itoa(endpointID))
	params.Set("status", strconv.Itoa(status))
	params.Set("sn", generateCommonAPISign(SuperAPIAppSecret, SuperAPIAppID))
	params.Set("ua", "1///"+SuperAPIAppID+"//")

	// 创建POST请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, bytes.NewBufferString(params.Encode()))
	if err != nil {
		return false, "创建请求失败"
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false, "系统错误"
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "系统错误"
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return false, "系统错误"
	}
	log.Info("result", zap.Any("result", result))

	// 检查响应状态
	if errno, ok := result["errno"].(float64); ok {
		if errno == 0 {
			return true, "success"
		} else {
			if errmsg, exists := result["errmsg"].(string); exists {
				return false, errmsg
			}
		}
	}

	return false, "系统错误"
}

// CancelBindings 取消设备绑定
func (c *Client) CancelBindings(ctx *gin.Context, imei string) (bool, string) {
	apiURL := ParentAdminHost + "/v1/machine/cancel_bindings"

	// 构建请求参数
	params := url.Values{}
	params.Set("imei", imei)
	params.Set("sn", generateParentAdminSign())

	// 创建GET请求
	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fullURL, nil)
	if err != nil {
		return false, "创建请求失败"
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false, "系统错误"
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "系统错误"
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return false, "系统错误"
	}
	log.Info("result", zap.Any("result", result))

	// 检查响应状态
	if status, ok := result["status"].(float64); ok {
		if status == 1 {
			return true, "success"
		} else if status == 0 {
			if msg, ok := result["errmsg"].(string); ok {
				return false, msg
			}
		}
	}

	return false, "系统错误"
}

// UnbindWatch 解绑手表
func (c *Client) UnbindWatch(ctx *gin.Context, imei string, username string) (bool, string) {
	apiURL := WatchAPIHost + "/api/account/factory"
	ts := strconv.FormatInt(time.Now().Unix(), 10)

	ua := []string{"1", "yx", "yx.readboy", WatchAPIAppID, "1", ""}
	uaStr := ""
	for i, part := range ua {
		if i > 0 {
			uaStr += "/"
		}
		uaStr += part
	}

	// 构建请求参数
	params := url.Values{}
	params.Set("imei", imei)
	params.Set("sn", generateWatchAPISign(ts))
	params.Set("t", ts)
	params.Set("ua", uaStr)
	params.Set("username", username)

	// 创建GET请求
	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fullURL, nil)
	if err != nil {
		return false, "创建请求失败"
	}
	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false, "系统错误"
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "系统错误"
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return false, "系统错误"
	}
	log.Info("result", zap.Any("result", result))

	// 检查响应状态
	if ok, exists := result["ok"].(float64); exists && ok == 1 {
		return true, "success"
	} else {
		if data, exists := result["data"]; exists {
			return false, fmt.Sprintf("%v", data)
		}
	}

	return false, "系统错误"
}

// generateParentAdminSign 生成家长管理后台API签名
func generateParentAdminSign() string {
	ts := strconv.FormatInt(time.Now().Unix(), 10)
	appidHash := fmt.Sprintf("%x", md5.Sum([]byte(ParentAdminAppID)))
	signContent := ts + ParentAdminAppSecret + appidHash
	signHash := fmt.Sprintf("%x", md5.Sum([]byte(signContent)))
	sn := "00000000" + ts + signHash + ParentAdminAppID
	return sn
}

// generateWatchAPISign 生成手表API签名
func generateWatchAPISign(ts string) string {
	ua := []string{"1", "yx", "yx.readboy", WatchAPIAppID, "1", ""}
	uaStr := ""
	for i, part := range ua {
		if i > 0 {
			uaStr += "/"
		}
		uaStr += part
	}

	signContent := uaStr + WatchAPIAppSecret + ts
	signHash := fmt.Sprintf("%x", md5.Sum([]byte(signContent)))
	return signHash
}

// generateCommonAPISign 通用的API签名生成函数
func generateCommonAPISign(appSecret, appID string) string {
	uid := "00000000"
	ts := strconv.FormatInt(time.Now().Unix(), 10)

	// 第一次MD5: MD5(uid)
	uidHash := fmt.Sprintf("%x", md5.Sum([]byte(uid)))

	// 第二次MD5: MD5(ts + APP_SECRET + MD5(uid))
	signContent := ts + appSecret + uidHash
	signHash := fmt.Sprintf("%x", md5.Sum([]byte(signContent)))

	// 组装签名: uid + ts + sign + appid
	sn := uid + ts + signHash + appID
	return sn
}
