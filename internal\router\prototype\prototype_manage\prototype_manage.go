package prototype_manage

import (
	"marketing-app/internal/cache"
	handler "marketing-app/internal/handler/prototype/prototype_manage"
	"marketing-app/internal/pkg/db"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	userRepo "marketing-app/internal/repository/user"
	warrantyBaseRepo "marketing-app/internal/repository/warranty/base"
	svc "marketing-app/internal/service/prototype/prototype_manage"

	"github.com/gin-gonic/gin"
)

type PrototypeManageRouter struct {
	prototypeHandler handler.Prototype
}

func NewPrototypeManageRouter() *PrototypeManageRouter {
	database, _ := db.GetDB()
	ptRepo := prototypeRepo.NewPrototypeRepo(database)
	wRepo := warrantyBaseRepo.NewWarranty(database)
	epRepo := endpointRepo.NewEndpoint(database)
	mtRepo := machineRepo.NewMachine(database)
	ptCache := cache.NewPrototypeCache()
	uRepo := userRepo.NewUser(database)
	prototypeSvc := svc.NewPrototype(ptRepo, wRepo, epRepo, mtRepo, ptCache, uRepo)
	return &PrototypeManageRouter{
		prototypeHandler: handler.NewPrototypeHandler(prototypeSvc),
	}
}

func (p *PrototypeManageRouter) Register(r *gin.RouterGroup) {
	g := r.Group("/prototype_manage")
	{
		g.POST("/create", p.prototypeHandler.CreatePrototype)
		g.GET("/list", p.prototypeHandler.ListPrototype)
		g.POST("/delete", p.prototypeHandler.DeletePrototype)
		g.GET("/stat", p.prototypeHandler.CheckPrototype)
		g.GET("/limit", p.prototypeHandler.GetPrototypeLimit)                     // 获取代理商样机限制信息
		g.GET("/user-list", p.prototypeHandler.GetPrototypeUserList)              // 获取代理商演示用户列表
		g.POST("/user-bind", p.prototypeHandler.BindPrototypeUser)                // 样机演示账号绑定
		g.POST("/change-user-phone", p.prototypeHandler.ChangePrototypeUserPhone) // 样机演示账号修改
		g.POST("/delete-user", p.prototypeHandler.DeletePrototypeUser)            // 终端演示用户删除
		g.GET("/warranty-query", p.prototypeHandler.PrototypeWarrantyQuery)       // 查询样机是否有保卡或者已经入样机库
		r.GET("/prototype/stat", p.prototypeHandler.CheckPrototype)
	}
}
