package endpoint

import (
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/endpoint/dto"
	"marketing-app/internal/pkg/convertor/endpoint_convertor"
	service "marketing-app/internal/service/endpoint"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Endpoint interface {
	GetEndpoints(c *gin.Context)
	GetEndpointOptions(c *gin.Context)
	GetEndpointImage(c *gin.Context)
	GetEndpointNewImage(c *gin.Context)
	PostEndpointNewImage(c *gin.Context)
	GetEndpointNotice(c *gin.Context)
	GetEndpointNoticeById(c *gin.Context)
	GetEndpointInfoByUsername(c *gin.Context)
	GetEndpointRegion(c *gin.Context)
	GetEndpointStat(c *gin.Context)
	GetEndpointDetail(c *gin.Context)
	GetSalesStat(c *gin.Context)
}

type endpoint struct {
	endpointSvc service.EndpointSvc
}

func NewEndpointHandler(svc service.EndpointSvc) Endpoint {
	return &endpoint{endpointSvc: svc}
}

// GetEndpoints 获取终端列表
func (e *endpoint) GetEndpoints(c *gin.Context) {
	var (
		req  dto.EndpointsRequest
		err  error
		resp *dto.EndpointsResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层获取终端列表
	r, err := e.endpointSvc.GetEndpoints(
		c,
		endpoint_convertor.NewEndpointsConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if len(r.Data) == 0 {
		err = errors.New("没找到相关网点")
		return
	}
	resp = &dto.EndpointsResponse{
		Data: r.Data,
	}
}

// GetEndpointOptions 获取终端选项
func (e *endpoint) GetEndpointOptions(c *gin.Context) {
	var (
		err  error
		resp *dto.EndpointOptionsResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 调用服务层获取终端选项
	r, err := e.endpointSvc.GetEndpointOptions(c)
	if err != nil {
		return
	}

	resp = &dto.EndpointOptionsResponse{
		ChannelLevel:     r.ChannelLevel,
		PositionPriority: r.PositionPriority,
		CompetitorBrand:  r.CompetitorBrand,
		ChannelType:      r.ChannelType,
	}
}

// GetEndpointImage 获取终端形象
func (e *endpoint) GetEndpointImage(c *gin.Context) {
	var (
		req  dto.EndpointImageRequest
		err  error
		resp *dto.EndpointImageResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层获取终端形象
	r, err := e.endpointSvc.GetEndpointImage(c, req.Endpoint)
	if err != nil {
		return
	}

	if r == nil {
		err = errors.Errorf("未找到终端(%d)的形象信息", req.Endpoint)
		return
	}

	resp = &dto.EndpointImageResponse{
		Name:             r.Name,
		Manager:          r.Manager,
		Phone:            r.Phone,
		Images:           r.Images,
		Lng:              r.Lng,
		Lat:              r.Lat,
		Code:             r.Code,
		Endpoint:         r.Endpoint,
		ChannelLevel:     r.ChannelLevel,
		ChannelType:      r.ChannelType,
		PositionPriority: r.PositionPriority,
		Area:             r.Area,
		Sales:            r.Sales,
		CreatedAt:        r.CreatedAt,
		Competitors:      r.Competitors,
	}
}

// GetEndpointNewImage 获取终端形象变更
func (e *endpoint) GetEndpointNewImage(c *gin.Context) {
	var (
		req  dto.EndpointNewImageRequest
		err  error
		resp *dto.EndpointNewImageResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层获取终端形象变更
	r, err := e.endpointSvc.GetEndpointNewImage(c, req.Endpoint)
	if err != nil {
		return
	}

	if r == nil {
		err = errors.Errorf("未找到终端(%d)的形象变更申请", req.Endpoint)
		return
	}

	resp = r
}

// PostEndpointNewImage 保存终端形象变更
func (e *endpoint) PostEndpointNewImage(c *gin.Context) {
	var (
		req dto.EndpointNewImagePostRequest
		err error
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, nil)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "参数错误，解析JSON格式失败")
		return
	}

	// 验证参数
	if req.ID == nil && req.Endpoint == nil {
		err = errors.New("参数错误，缺少【变更申请编号】或【终端编号】")
		return
	}

	// 调用服务层保存终端形象变更
	ok, errMsg := e.endpointSvc.SaveEndpointNewImage(c, &req)
	if !ok {
		err = errors.New(errMsg)
		return
	}
}

// GetEndpointNotice 获取终端公告
func (e *endpoint) GetEndpointNotice(c *gin.Context) {
	var (
		req  dto.EndpointNoticeRequest
		err  error
		resp *dto.EndpointNoticeResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 设置默认值
	if req.Count == 0 {
		req.Count = 10
	}

	// 调用服务层获取终端公告
	r, err := e.endpointSvc.GetEndpointNotice(c, req.Ts, req.Count)
	if err != nil {
		return
	}

	if r == nil || len(r.Data) == 0 {
		err = errors.New("暂时没有公告")
		return
	}

	resp = r
}

// GetEndpointNoticeById 根据ID获取终端公告
func (e *endpoint) GetEndpointNoticeById(c *gin.Context) {
	var (
		req  dto.EndpointNoticeByIdRequest
		err  error
		resp *dto.NoticeItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 调用服务层根据ID获取终端公告
	r, err := e.endpointSvc.GetEndpointNoticeById(c, req.NoticeID)
	if err != nil {
		return
	}

	if r == nil {
		err = errors.New("暂时没有公告")
		return
	}

	resp = r
}

// GetEndpointInfoByUsername 根据用户名获取终端信息
func (e *endpoint) GetEndpointInfoByUsername(c *gin.Context) {
	var (
		req  dto.EndpointInfoByUsernameRequest
		err  error
		resp *dto.EndpointInfoByUsernameResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层根据用户名获取终端信息
	resp, err = e.endpointSvc.GetEndpointInfoByUsername(c, req.Username)
	if err != nil {
		return
	}

	if resp == nil {
		err = errors.New("未找到数据")
		return
	}
}

// GetEndpointRegion 获取终端区域
func (e *endpoint) GetEndpointRegion(c *gin.Context) {
	var (
		req  dto.EndpointRegionRequest
		err  error
		resp *dto.EndpointRegionResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 调用服务层获取终端区域
	r, err := e.endpointSvc.GetEndpointRegion(c, int(uid))
	if err != nil {
		return
	}

	if r == nil || len(r.Data) == 0 {
		err = errors.New("未找到数据")
		return
	}

	resp = r
}

// GetEndpointStat 获取终端统计
func (e *endpoint) GetEndpointStat(c *gin.Context) {
	var (
		req  dto.EndpointStatRequest
		err  error
		resp *dto.EndpointStatResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层获取终端统计
	r, err := e.endpointSvc.GetEndpointStat(c, req.TopAgency, req.SecondAgency)
	if err != nil {
		return
	}

	if r == nil || len(r.Data) == 0 {
		err = errors.New("未找到数据")
		return
	}

	resp = r
}

// GetEndpointDetail 获取终端详情
func (e *endpoint) GetEndpointDetail(c *gin.Context) {
	var (
		req  dto.EndpointDetailRequest
		err  error
		resp *dto.EndpointDetailResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	req.Uid = int(uid)

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Count == 0 {
		req.Count = 10
	}

	// 调用服务层获取终端详情
	r, err := e.endpointSvc.GetEndpointDetail(c, req.Uid, req.TopAgency, req.SecondAgency, req.Page, req.Count)
	if err != nil {
		return
	}

	if r == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = r
}

// GetSalesStat 获取销售统计
func (e *endpoint) GetSalesStat(c *gin.Context) {
	var (
		req  dto.SalesStatRequest
		err  error
		resp *dto.SalesStatResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层获取销售统计数据
	r, err := e.endpointSvc.GetSalesStat(c, req.AccessToken)
	if err != nil {
		return
	}

	resp = r
}
