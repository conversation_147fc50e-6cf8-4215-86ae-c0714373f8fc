package training

import (
	"errors"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/training/dto"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Training interface {
	GetTrainVideos(c *gin.Context, ts int64, count int) (*dto.TrainVideosResponse, error)
	GetTrainList(c *gin.Context, page, count, trainType, category, order int, uid uint) (*dto.TrainListResponse, error)
	GetTrainTypes(c *gin.Context) ([]dto.TrainTypeItem, error)
	GetTrainCategories(c *gin.Context, trainType int) ([]dto.TrainCategoryItem, error)
	GetTrainTypeCategories(c *gin.Context) ([]dto.TrainTypeCategoryItem, error)
	AddLearningHistory(c *gin.Context, trainID uint, uid uint, learningTime string) error
	AddShareHistory(c *gin.Context, trainID uint, uid uint) (int, error)
	SearchTrains(c *gin.Context, keyword string, uid uint) ([]dto.TrainSearchItem, error)
	GetTrainHot(c *gin.Context, page, count int) (*dto.TrainHotResponse, error)
	AddCollection(c *gin.Context, trainID uint, uid uint) error
	CancelCollection(c *gin.Context, trainID uint, uid uint) error
	GetCollectionList(c *gin.Context, uid uint, page, count int) (*dto.CollectionListResponse, error)
	GetTrainBanner(c *gin.Context, trainType int, uid uint) ([]dto.TrainBannerItem, error)
}

type training struct {
	db *gorm.DB
}

func NewTraining(db *gorm.DB) Training {
	return &training{
		db: db,
	}
}

func (t *training) GetTrainVideos(c *gin.Context, ts int64, count int) (*dto.TrainVideosResponse, error) {
	var videos []model.TrainVideos

	// 将时间戳转换为时间
	var startTime time.Time
	if ts <= 0 {
		startTime = time.Now()
	} else {
		startTime = time.Unix(ts, 0)
	}

	// 查询count+1条记录用于判断是否还有更多数据
	query := t.db.WithContext(c).Model(&model.TrainVideos{}).
		Select("id, title, preview, path, description, created_at, updated_at").
		Where("created_at < ? AND status = ?", startTime, 1).
		Order("created_at DESC").
		Limit(count + 1)

	if err := query.Find(&videos).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回nil
	if len(videos) == 0 {
		return nil, nil
	}

	// 判断是否已经到最后一页
	isEnd := len(videos) < count+1
	size := len(videos)
	if !isEnd {
		size = count
	}

	// 获取实际返回的数据（最多count条）
	actualData := videos[:size]

	// 转换为DTO格式
	data := make([]dto.TrainVideoItem, len(actualData))
	for i, video := range actualData {
		data[i] = dto.TrainVideoItem{
			ID:          video.ID,
			Title:       video.Title,
			Preview:     video.Preview,
			Path:        video.Path,
			Description: video.Description,
			CreatedAt:   video.CreatedAt,
			UpdatedAt:   video.UpdatedAt,
		}
	}

	return &dto.TrainVideosResponse{
		Data:  data,
		Size:  size,
		IsEnd: isEnd,
	}, nil
}

func (t *training) GetTrainList(c *gin.Context, page, count, trainType, category, order int, uid uint) (*dto.TrainListResponse, error) {
	// 计算offset
	offset := (page - 1) * count
	if offset < 0 {
		offset = 0
	}

	// 确定排序字段
	orderField := "t.id DESC"
	if order == 2 {
		orderField = "t.month_count DESC"
	}

	// 构建基础查询
	query := t.db.WithContext(c).Table("train t").
		Select(`t.id, t.name, t.preview, t.path, t.article_link, t.share, t.description, t.created_at, 
				t.credit, t.credit_learning_time, IF(turh.credit > 0, 1, 0) AS get_credit,
				t.updated_at, t.star, t.download_count, IF(tc2.train_id IS NOT NULL, 1, 0) AS collection,
				(SELECT COUNT(1) FROM train_collection tc WHERE tc.train_id = t.id) AS collection_count,
				(SELECT MAX(tlh.created_at) FROM train_learning_history tlh 
				 WHERE tlh.user_id = ? AND tlh.target_id = t.id AND tlh.created_at > t.updated_at) AS read_time`,
			uid).
		Joins("LEFT JOIN train_collection tc2 ON (t.id = tc2.train_id AND tc2.user_id = ?)", uid).
		Joins("LEFT JOIN train_user_credit_history turh ON (turh.user_id = ? AND turh.type = 1 AND turh.source_id = t.id)", uid).
		Where("t.status = 1 AND t.banner = 0")

	// 如果指定了分类，需要 JOIN 分类关系表
	if category > 0 {
		query = query.Joins("RIGHT JOIN train_category_relation tcr ON t.id = tcr.train_id").
			Where("tcr.category_id = ?", category)
	}

	// 如果指定了类型
	if trainType > 0 {
		query = query.Where("t.type = ?", trainType)
	}

	// 添加排序和分页
	query = query.Order("t.top DESC").Order(orderField).
		Offset(offset).Limit(count + 1)

	// 执行查询
	var results []dto.TrainItem
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回空结果
	if len(results) == 0 {
		return &dto.TrainListResponse{
			Data:  []dto.TrainItem{},
			Size:  0,
			IsEnd: true,
		}, nil
	}

	// 判断是否已经到最后一页
	isEnd := len(results) < count+1
	size := len(results)
	if !isEnd {
		size = count
	}

	// 获取实际返回的数据（最多count条）
	actualData := results[:size]

	return &dto.TrainListResponse{
		Data:  actualData,
		Size:  size,
		IsEnd: isEnd,
	}, nil
}

func (t *training) GetTrainTypes(c *gin.Context) ([]dto.TrainTypeItem, error) {
	var results []dto.TrainTypeItem

	// 构建查询，只获取在train表中存在有效数据的类型
	query := t.db.WithContext(c).Table("train_type tt").
		Select("tt.id, tt.title").
		Where(`(SELECT id FROM train WHERE status = 1 AND type = tt.id LIMIT 1) > 0`).
		Order("tt.`order` ASC")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (t *training) GetTrainCategories(c *gin.Context, trainType int) ([]dto.TrainCategoryItem, error) {
	var results []dto.TrainCategoryItem

	// 构建基础查询
	query := t.db.WithContext(c).Table("train_category tc").
		Select("DISTINCT tc.id, tc.title").
		Joins("LEFT JOIN train_category_relation tcr ON tc.id = tcr.category_id").
		Joins("LEFT JOIN train t ON tcr.train_id = t.id").
		Where("t.status = 1 AND t.banner = 0").
		Order("tc.`order`")

	// 如果指定了培训类型，添加类型条件
	if trainType > 0 {
		query = query.Where("t.type = ?", trainType)
	}

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (t *training) GetTrainTypeCategories(c *gin.Context) ([]dto.TrainTypeCategoryItem, error) {
	var results []dto.TrainTypeCategoryItem

	query := t.db.WithContext(c).Table("(SELECT type, category FROM train WHERE category > 0 GROUP BY type, category) AS t").
		Select("t.type, t.category, c.title").
		Joins("LEFT JOIN train_category c ON t.category = c.id")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (t *training) AddLearningHistory(c *gin.Context, trainID uint, uid uint, learningTime string) error {
	// 验证trainID
	if trainID == 0 {
		return errors.New("培训ID不能为空")
	}

	// 开始事务
	return t.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新train表的increment和download_count字段
		if err := tx.Model(&model.Train{}).
			Where("id = ?", trainID).
			UpdateColumns(map[string]interface{}{
				"increment":      gorm.Expr("increment + 1"),
				"download_count": gorm.Expr("download_count + 1"),
			}).Error; err != nil {
			return err
		}

		// 更新train表的daily_count和month_count字段
		if err := tx.Model(&model.Train{}).
			Where("id = ?", trainID).
			UpdateColumns(map[string]interface{}{
				"daily_count": gorm.Expr("daily_count + 1"),
				"month_count": gorm.Expr("month_count + 1"),
			}).Error; err != nil {
			return err
		}

		// 将学习时长字符串转换为uint16
		learningTimeInt, err := strconv.ParseUint(learningTime, 10, 16)
		if err != nil {
			return errors.New("学习时长格式不正确")
		}

		// 插入学习历史记录
		learningHistory := &model.TrainLearningHistory{
			TargetID:     trainID,
			UserID:       uid,
			LearningTime: uint16(learningTimeInt),
			Target:       "train",
			CreatedAt:    time.Now(),
		}

		if err := tx.Create(learningHistory).Error; err != nil {
			return err
		}

		return nil
	})
}

func (t *training) AddShareHistory(c *gin.Context, trainID uint, uid uint) (int, error) {
	// 验证trainID
	if trainID == 0 {
		return 0, errors.New("培训ID不能为空")
	}

	// 检查培训是否存在且可分享
	var count int64
	if err := t.db.WithContext(c).Model(&model.Train{}).
		Where("id = ? AND share = 1 AND status = 1", trainID).
		Count(&count).Error; err != nil {
		return 0, err
	}

	// 如果培训不可分享，返回-1
	if count == 0 {
		return -1, nil
	}

	// 插入分享历史记录
	shareHistory := &model.TrainShareHistory{
		UserID:    uid,
		TrainID:   trainID,
		CreatedAt: time.Now(),
	}

	if err := t.db.WithContext(c).Create(shareHistory).Error; err != nil {
		return 0, err
	}

	return 1, nil
}

func (t *training) SearchTrains(c *gin.Context, keyword string, uid uint) ([]dto.TrainSearchItem, error) {
	var results []dto.TrainSearchItem

	// 构建与Python版本完全一致的搜索查询
	query := t.db.WithContext(c).Table("train t").
		Select(`DISTINCT t.id, t.name, t.preview, t.path, t.article_link, t.share, t.description, 
			t.created_at, t.updated_at, t.star, t.download_count, t.credit, t.credit_learning_time,
			IF(turh.credit > 0, 1, 0) AS get_credit`).
		Joins("LEFT JOIN train_user_credit_history turh ON (turh.user_id = ? AND turh.type = 1 AND turh.source_id = t.id)", uid).
		Where("(t.name LIKE ? OR t.description LIKE ?) AND t.status = 1", "%"+keyword+"%", "%"+keyword+"%")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (t *training) GetTrainHot(c *gin.Context, page, count int) (*dto.TrainHotResponse, error) {
	// 计算offset
	offset := (page - 1) * count
	if offset < 0 {
		offset = 0
	}

	// 构建与Python版本完全一致的查询
	var results []dto.TrainHotItem
	query := t.db.WithContext(c).Model(&model.Train{}).
		Select("id, name, preview, path, description, article_link, share, created_at, updated_at, star, download_count").
		Where("status = 1").
		Order("recommend DESC").
		Order("month_count DESC").
		Offset(offset).
		Limit(count + 1)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回nil
	if len(results) == 0 {
		return nil, nil
	}

	// 判断是否已经到最后一页
	isEnd := len(results) < count+1
	size := len(results)
	if !isEnd {
		size = count
	}

	// 获取实际返回的数据（最多count条）
	actualData := results[:size]

	return &dto.TrainHotResponse{
		Data:  actualData,
		Size:  size,
		IsEnd: isEnd,
	}, nil
}

func (t *training) AddCollection(c *gin.Context, trainID uint, uid uint) error {
	// 验证参数
	if trainID == 0 {
		return errors.New("培训ID不能为空")
	}
	if uid == 0 {
		return errors.New("用户ID不能为空")
	}

	// 检查培训是否存在且有效
	var count int64
	if err := t.db.WithContext(c).Model(&model.Train{}).
		Where("(status = 1 OR banner = 1) AND id = ?", trainID).
		Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		return errors.New("未找到数据，无法收藏")
	}

	// 检查是否已经收藏
	var collectionCount int64
	if err := t.db.WithContext(c).Model(&model.TrainCollection{}).
		Where("train_id = ? AND user_id = ?", trainID, uid).
		Count(&collectionCount).Error; err != nil {
		return err
	}

	if collectionCount > 0 {
		return errors.New("此记录已被收藏")
	}

	// 添加收藏记录（使用INSERT IGNORE等效操作）
	collection := &model.TrainCollection{
		TrainID:   trainID,
		UserID:    uid,
		CreatedAt: time.Now(),
	}

	if err := t.db.WithContext(c).Create(collection).Error; err != nil {
		return err
	}

	return nil
}

func (t *training) CancelCollection(c *gin.Context, trainID uint, uid uint) error {
	// 验证参数
	if trainID == 0 {
		return errors.New("培训ID不能为空")
	}
	if uid == 0 {
		return errors.New("用户ID不能为空")
	}

	// 检查培训是否存在且有效
	var count int64
	if err := t.db.WithContext(c).Model(&model.Train{}).
		Where("(status = 1 OR banner = 1) AND id = ?", trainID).
		Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		return errors.New("未找到数据，无法取消收藏")
	}

	// 检查是否已经收藏
	var collectionCount int64
	if err := t.db.WithContext(c).Model(&model.TrainCollection{}).
		Where("train_id = ? AND user_id = ?", trainID, uid).
		Count(&collectionCount).Error; err != nil {
		return err
	}

	if collectionCount == 0 {
		return errors.New("未找到收藏记录, 无法取消收藏")
	}

	// 删除收藏记录
	if err := t.db.WithContext(c).
		Where("train_id = ? AND user_id = ?", trainID, uid).
		Delete(&model.TrainCollection{}).Error; err != nil {
		return err
	}

	return nil
}

func (t *training) GetCollectionList(c *gin.Context, uid uint, page, count int) (*dto.CollectionListResponse, error) {
	// 计算offset
	offset := (page - 1) * count
	if offset < 0 {
		offset = 0
	}

	// 构建与Python版本完全一致的复杂查询
	var results []dto.CollectionListItem
	query := t.db.WithContext(c).Table("train t").
		Select(`DISTINCT t.id, t.name, t.preview, t.path, t.article_link, t.share, t.description, 
			t.created_at, t.updated_at, t.star, t.download_count, IF(tc.train_id, 1, 0) AS collection,
			(SELECT COUNT(1) FROM train_collection tc2 WHERE tc2.train_id = t.id) AS collection_count,
			(SELECT MAX(tlh.created_at) FROM train_learning_history tlh 
			 WHERE tlh.user_id = ? AND tlh.target_id = t.id AND tlh.created_at > t.updated_at) AS read_time`, uid).
		Joins("LEFT JOIN train_collection tc ON t.id = tc.train_id").
		Where("tc.user_id = ?", uid).
		Group("t.id").
		Offset(offset).
		Limit(count + 1)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回空结果
	if len(results) == 0 {
		return &dto.CollectionListResponse{
			Data:  []dto.CollectionListItem{},
			Size:  0,
			IsEnd: true,
		}, nil
	}

	// 判断是否已经到最后一页
	isEnd := len(results) < count+1
	size := len(results)
	if !isEnd {
		size = count
	}

	// 获取实际返回的数据（最多count条）
	actualData := results[:size]

	return &dto.CollectionListResponse{
		Data:  actualData,
		Size:  size,
		IsEnd: isEnd,
	}, nil
}

func (t *training) GetTrainBanner(c *gin.Context, trainType int, uid uint) ([]dto.TrainBannerItem, error) {
	var results []dto.TrainBannerItem

	query := t.db.WithContext(c).Table("train t").
		Select(`DISTINCT t.id, t.name, t.preview, t.path, t.article_link, t.share, t.description,
			t.created_at, t.updated_at, t.star, t.download_count, t.banner_image,
			IF(tc.train_id, 1, 0) AS collection`).
		Joins("LEFT JOIN train_collection tc ON (t.id = tc.train_id AND tc.user_id = ?)", uid).
		Where("t.type = ? AND t.banner = 1", trainType)

	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}
