package repository

import (
	"errors"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/dto"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminUserRepository interface {
	// GetAdminUserByID 根据ID获取管理员用户信息
	GetAdminUserByID(c *gin.Context, id int64) (*model.AdminUser, error)
	// GetAdminUserByUsername 根据用户名获取管理员用户信息
	GetAdminUserByUsername(c *gin.Context, username string) (*model.AdminUser, error)
	// GetAdminUserByPhone 根据手机号获取管理员用户信息
	GetAdminUserByPhone(c *gin.Context, phone string) (*model.AdminUser, error)
	// GetByQwUserID 根据企业微信用户ID获取管理员用户信息
	GetByQwUserID(c *gin.Context, QwUserID string) (*model.AdminUser, error)
	//	UpdateUserActiveTime 更新用户活跃时间
	UpdateUserActiveTime(c *gin.Context, id uint) error
	// CheckRole 校验用户角色
	CheckRole(c *gin.Context, uid int, appType int) (roles []*dto.Role, err error)
	// GetUserEndpoint 获取用户终端
	GetUserEndpoint(c *gin.Context, uid uint) (*model.Endpoint, error)
}

type adminUserRepo struct {
	db *gorm.DB //
}

func NewAdminUserRepository(db *gorm.DB) AdminUserRepository {
	return &adminUserRepo{db: db}
}

func (r *adminUserRepo) GetAdminUserByID(c *gin.Context, id int64) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (r *adminUserRepo) GetAdminUserByUsername(c *gin.Context, username string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (r *adminUserRepo) GetAdminUserByPhone(c *gin.Context, phone string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (r *adminUserRepo) GetByQwUserID(c *gin.Context, QwUserID string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("qw_user_id = ?", QwUserID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err // 其他错误
	}
	return &user, nil
}

// UpdateUserActiveTime 更新用户活跃状态
func (r *adminUserRepo) UpdateUserActiveTime(c *gin.Context, id uint) error {
	return r.db.WithContext(c).Model(&model.AdminUser{}).Where("id = ?", id).Update("actived_at", time.Now().Format(time.DateTime)).Error
}

func (r *adminUserRepo) CheckRole(c *gin.Context, uid int, appType int) (roles []*dto.Role, err error) {
	if appType == -1 {
		err = r.db.WithContext(c).Table("admin_users").Select("r.id, r.slug, r.name").
			Joins("RIGHT JOIN admin_role_users ru on admin_users.id=ru.user_id").
			Joins("LEFT JOIN admin_roles r on ru.role_id=r.id").
			Where("admin_users.id = ?", uid).
			Scan(&roles).Error
	} else {
		err = r.db.WithContext(c).Table("admin_users").Select("r.id, r.slug, r.name").
			Joins("RIGHT JOIN admin_role_users ru on admin_users.id=ru.user_id").
			Joins("LEFT JOIN admin_roles r on ru.role_id=r.id").
			Where("admin_users.id = ?", uid).
			Where("r.app_type = ? or r.app_type = 100", appType).
			Scan(&roles).Error
	}
	return roles, err
}

func (r *adminUserRepo) GetUserEndpoint(c *gin.Context, uid uint) (*model.Endpoint, error) {
	var endpoint model.Endpoint
	err := r.db.WithContext(c).
		Table("user_endpoint ue").
		Select("e.id, e.name, e.type, e.top_agency").
		Joins("INNER JOIN endpoint e ON ue.endpoint = e.id").
		Where("ue.uid = ?", uid).
		Take(&endpoint).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &endpoint, nil
}
