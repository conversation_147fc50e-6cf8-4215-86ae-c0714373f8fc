package client

type CreatePrototypeRequest struct {
	Uid        int    `json:"uid"`
	Barcode    string `json:"barcode" binding:"required"`
	EndpointId int    `json:"endpoint_id" binding:"required"`
}

type ListPrototypeRequest struct {
	EndpointId int `json:"endpoint_id" binding:"required"`
	Page       int `json:"page" binding:"required"`
	PageSize   int `json:"page_size" binding:"required"`
}

type DeletePrototypeRequest struct {
	Barcode    string `json:"barcode" binding:"required"`
	EndpointID int    `json:"endpoint_id" binding:"required"`
}

type CheckPrototypeRequest struct {
	Barcode string `json:"barcode" binding:"required" form:"barcode"`
}
