package service

import (
	"errors"
	"fmt"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/repository"
	"time"

	"github.com/gin-gonic/gin"
)

type PromotionService interface {
	GetList(c *gin.Context, param *dto.PromotionListsReq) (*dto.PromotionListsResp, error)
	GetDetail(c *gin.Context, param *dto.PromotionListDetailReq) (*dto.PromotionListDetailResp, error)
	UploadReceipt(c *gin.Context, param *dto.ReceiptUploadReq, endpoint int) error
	GetPromotions(c *gin.Context, param *dto.PromotionsReq) (*dto.PromotionsResp, error)
}

type promotionService struct {
	repo repository.PromotionListRepository
}

func NewPromotionService(repo repository.PromotionListRepository) PromotionService {
	return &promotionService{
		repo: repo,
	}
}

func (s *promotionService) GetList(c *gin.Context, param *dto.PromotionListsReq) (*dto.PromotionListsResp, error) {
	// 获取列表数据
	items, total, err := s.repo.GetList(c, param)
	if err != nil {
		return nil, err
	}

	// 收集ID用于查询关联数据
	var ids []int
	var warrantyIds []int
	for _, item := range items {
		ids = append(ids, item.Id)
		warrantyIds = append(warrantyIds, item.WarrantyId)
	}

	// 获取回执信息
	if len(ids) > 0 {
		receipts, err := s.repo.GetReceiptsByListIds(c, ids)
		if err != nil {
			return nil, err
		}

		// 终端模式：获取保修信息
		warranties, err := s.repo.GetWarrantyInfoByIds(c, warrantyIds)
		if err != nil {
			return nil, err
		}

		// 处理回执和保修信息
		for i, item := range items {
			// 设置回执
			for _, receipt := range receipts {
				if item.Id == receipt.SalesPromotionListId {
					items[i].Receipt = receipt.Receipt
					break
				}
			}

			// 设置保修信息
			if warranty, exists := warranties[item.WarrantyId]; exists {
				items[i].Number = warranty.Number
				items[i].ActivatedAt = warranty.ActivatedAtOld
				items[i].StudentName = warranty.StudentName
			}
		}
	}

	return &dto.PromotionListsResp{
		List:     items,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}

func (s *promotionService) GetDetail(c *gin.Context, param *dto.PromotionListDetailReq) (*dto.PromotionListDetailResp, error) {
	// 获取详情数据
	detail, err := s.repo.GetDetail(c, param.Id)
	if err != nil {
		return nil, err
	}

	if detail == nil {
		return nil, errors.New("记录不存在")
	}

	if detail.Endpoint != param.Endpoint {
		return nil, fmt.Errorf("无权查看")
	}

	// 获取回执信息
	receipts, err := s.repo.GetReceiptsByListIds(c, []int{param.Id})
	if err != nil {
		return nil, err
	}

	if len(receipts) > 0 {
		detail.Receipt = receipts[0].Receipt
	}

	return detail, nil
}

func (s *promotionService) UploadReceipt(c *gin.Context, param *dto.ReceiptUploadReq, endpoint int) error {
	// 查询记录是否存在
	detail, err := s.repo.GetDetail(c, param.Id)
	if err != nil {
		return err
	}

	if detail == nil {
		return errors.New("记录不存在")
	}

	// 检查权限
	if detail.Endpoint != endpoint {
		return fmt.Errorf("无权操作")
	}

	// 获取促销活动详情
	promotion, err := s.repo.GetPromotionDetail(c, detail.SalesPromotionId)
	if err != nil {
		return err
	}

	// 检查上传时间
	if promotion.ReceiptDay != nil {
		delayTime := promotion.ReceiptDay.Add(24 * time.Hour)
		if delayTime.Before(time.Now()) {
			return fmt.Errorf("回执上传已经过了截止时间")
		}
	}

	// 更新回执
	return s.repo.UpdateReceipt(c, param.Id, param.Receipt)
}

func (s *promotionService) GetPromotions(c *gin.Context, param *dto.PromotionsReq) (*dto.PromotionsResp, error) {
	// 获取促销活动列表
	items, total, err := s.repo.GetPromotions(c, param)
	if err != nil {
		return nil, err
	}

	// 收集活动ID
	var ids []int
	for _, item := range items {
		ids = append(ids, item.Id)
	}

	// 获取统计数据
	if len(ids) > 0 {
		listCounts, receiptCounts, err := s.repo.GetPromotionsCounts(c, ids, param)
		if err != nil {
			return nil, err
		}

		// 创建统计数据映射
		listCountMap := make(map[int]int)
		receiptCountMap := make(map[int]int)

		for _, count := range listCounts {
			listCountMap[count.Id] = count.ListCount
		}

		for _, count := range receiptCounts {
			receiptCountMap[count.Id] = count.ReceiptCount
		}

		// 设置统计数据
		for i, item := range items {
			if listCount, exists := listCountMap[item.Id]; exists {
				items[i].ListCount = listCount
			}
			if receiptCount, exists := receiptCountMap[item.Id]; exists {
				items[i].ReceiptCount = receiptCount
			}
		}
	}

	return &dto.PromotionsResp{
		List:     items,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}
