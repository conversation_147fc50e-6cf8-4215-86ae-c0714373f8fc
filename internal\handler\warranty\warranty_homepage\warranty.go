package warranty_homepage

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/warranty/warranty_homepage/dto"
	"marketing-app/internal/pkg/convertor/warranty_convertor"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/router/warranty/warranty_homepage/client"
	service "marketing-app/internal/service/warranty/warranty_homepage"
	"time"
)

type WarrantyBaseHandler interface {
	ListWarranties(c *gin.Context)
	GetWarrantyCardByIdentifier(c *gin.Context)
}

type Warranty struct {
	warrantySvc service.WarrantyBaseService
}

func NewWarrantyHandler(warrantySvc service.WarrantyBaseService) WarrantyBaseHandler {
	return &Warranty{
		warrantySvc: warrantySvc,
	}
}

func (w *Warranty) ListWarranties(c *gin.Context) {
	var (
		req  client.WarrantyRequest
		err  error
		resp dto.WarrantyBaseResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}

	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	req.Uid = int(uid)

	if err = utils.DateFormatValidate(time.DateOnly, req.BuyDateStart, req.BuyDateEnd); err != nil {
		err = errors.Wrap(err, "date format error")
		return
	}
	r, total, err := w.warrantySvc.GetList(
		c,
		warranty_convertor.NewWarrantyBaseConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = dto.WarrantyBaseResp{
		Total:        total,
		WarrantyBase: r,
	}
}

func (w *Warranty) GetWarrantyCardByIdentifier(c *gin.Context) {
	var (
		req  client.IdentifierQueryRequest
		err  error
		resp dto.WarrantyDetailsResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	req.Uid = int(uid)

	identifier := c.Query("identifier")
	req.Identifier = identifier
	r, total, err := w.warrantySvc.GetByIdentifier(
		c,
		warranty_convertor.NewIdentifierConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = dto.WarrantyDetailsResp{
		Total:           total,
		WarrantyDetails: r,
	}
}
