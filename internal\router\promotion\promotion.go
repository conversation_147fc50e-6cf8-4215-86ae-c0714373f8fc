package promotion

import (
	handler "marketing-app/internal/handler"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type PromotionRouter struct {
	promotionHandler handler.PromotionHandler
}

func NewPromotionListRouter() *PromotionRouter {
	database, _ := db.GetDB()
	repo := repository.NewPromotionListRepository(database)
	svc := service.NewPromotionService(repo)
	userSvc := service.NewAdminUserService(repository.NewAdminUserRepository(database), nil, nil)
	h := handler.NewPromotionListHandler(svc, userSvc)
	return &PromotionRouter{
		promotionHandler: h,
	}
}

func (p *PromotionRouter) Register(r *gin.RouterGroup) {
	g := r.Group("")
	{
		// 促销活动列表
		g.GET("", p.promotionHandler.GetPromotions)
		// 名单列表
		g.GET("/list", p.promotionHandler.GetList)
		// 名单详情
		g.GET("/list/:id", p.promotionHandler.GetDetail)
		// 上传回执
		g.POST("/list/:id", p.promotionHandler.UploadReceipt)
	}
}
