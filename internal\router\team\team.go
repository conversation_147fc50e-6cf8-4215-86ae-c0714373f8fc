package team

import (
	handler "marketing-app/internal/handler/team"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository/endpoint/user_endpoint"
	teamRepo "marketing-app/internal/repository/team"
	service "marketing-app/internal/service/team"

	"github.com/gin-gonic/gin"
)

type TeamRouter struct {
	teamHandler handler.Team
}

func NewTeamRouter() *TeamRouter {
	database, _ := db.GetDB()

	// 创建团队相关的repository
	teamRepository := teamRepo.NewTeamRepo(database)
	userEndpointRepository := user_endpoint.NewUserEndpoint(database)

	// 创建团队service
	teamSvc := service.NewTeamSvc(teamRepository, userEndpointRepository)

	return &TeamRouter{
		teamHandler: handler.NewTeamHandler(teamSvc),
	}
}

func (t *TeamRouter) Register(r *gin.RouterGroup) {
	g := r.Group("")
	{
		g.GET("/info", t.teamHandler.GetTeamInfo) // 获取团队信息
	}
}
