package model

import (
	"time"
)

type TrainLearningHistory struct {
	ID           uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID       uint      `gorm:"type:int(10) unsigned;not null" json:"user_id"`
	Target       string    `gorm:"type:enum('train','course');not null;comment:学习资源，train-推培，course-课程" json:"target"`
	TargetID     uint      `gorm:"type:int(10) unsigned;not null;comment:学习资源id" json:"target_id"`
	LearningTime uint16    `gorm:"type:smallint(5) unsigned;not null;comment:学习时长，秒" json:"learning_time"`
	Version      uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:2-学分计时版" json:"version"`
	CreatedAt    time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (TrainLearningHistory) TableName() string {
	return "train_learning_history"
}
