package tablet_control

import (
	tabletControlClient "marketing-app/internal/api/client/tablet_control"
	"marketing-app/internal/handler/tablet_control/dto"
	machineRepo "marketing-app/internal/repository/machine"
	"marketing-app/internal/repository/machine/machine_type/builder"
	prototypeRepo "marketing-app/internal/repository/prototype"
	tabletControlRepo "marketing-app/internal/repository/tablet_control"
	"marketing-app/internal/service/tablet_control/entity"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TabletControlSvc interface {
	// AllowUnknownApp 设置允许未知应用安装状态
	AllowUnknownApp(c *gin.Context, req *entity.AllowUnknownAppRequest) error
	// VerifyPhoneCodeForAllowUnknownApp 验证手机验证码
	VerifyPhoneCodeForAllowUnknownApp(c *gin.Context, req *entity.VerifyPhoneCodeForAllowUnknownAppRequest) error
	// GetAllowNsfwUrl 获取设备NSFW URL允许状态
	GetAllowNsfwUrl(c *gin.Context, req *entity.GetAllowNsfwUrlRequest) (interface{}, error)
	// SetAllowNsfwUrl 设置设备NSFW URL允许状态
	SetAllowNsfwUrl(c *gin.Context, req *entity.SetAllowNsfwUrlRequest) error
	// CancelBindings 取消设备绑定
	CancelBindings(c *gin.Context, req *entity.CancelBindingsRequest) error
	// UnbindWatch 解绑手表
	UnbindWatch(c *gin.Context, req *entity.UnbindWatchRequest) error
}

type tabletControlSvc struct {
	tabletControlRepo tabletControlRepo.TabletControl
	prototypeRepo     prototypeRepo.Prototype
	machineRepo       machineRepo.Machine
	apiClient         *tabletControlClient.Client
}

func NewTabletControl(tabletControlRepo tabletControlRepo.TabletControl, prototypeRepo prototypeRepo.Prototype, machineRepo machineRepo.Machine) TabletControlSvc {
	return &tabletControlSvc{
		tabletControlRepo: tabletControlRepo,
		prototypeRepo:     prototypeRepo,
		machineRepo:       machineRepo,
		apiClient:         tabletControlClient.NewClient(),
	}
}

// AllowUnknownApp 设置允许未知应用安装状态
func (s *tabletControlSvc) AllowUnknownApp(c *gin.Context, req *entity.AllowUnknownAppRequest) error {
	// 验证参数
	if err := s.validateAllowUnknownAppRequest(req); err != nil {
		return err
	}

	// 查找设备序列号
	deviceNumber, err := s.findDeviceNumber(c, req.Barcode, req.Number, req.IMEI)
	if err != nil {
		return err
	}

	// 调用外部API设置允许未知应用状态
	success, message := s.apiClient.AllowUnknownApp(c, deviceNumber, req.EndpointID, req.Status)
	if !success {
		return errors.New(message)
	}

	return nil
}

// validateAllowUnknownAppRequest 验证AllowUnknownApp请求参数
func (s *tabletControlSvc) validateAllowUnknownAppRequest(req *entity.AllowUnknownAppRequest) error {
	if req.Barcode == "" && req.Number == "" && req.IMEI == "" {
		return errors.New("barcode、number、imei不能全部为空")
	}
	if req.EndpointID == 0 {
		return errors.New("参数错误")
	}
	if req.Status != 0 && req.Status != 1 {
		return errors.New("状态值必须为0（禁止）或1（允许）")
	}
	return nil
}

// findDeviceNumber 查找设备序列号，优先查样机库，再查保卡表
func (s *tabletControlSvc) findDeviceNumber(c *gin.Context, barcode, number, imei string) (string, error) {
	// 首先查询样机库
	prototype, err := s.prototypeRepo.QueryPrototype(c, barcode, number, imei)
	if err != nil {
		return "", errors.Wrap(err, "查询样机信息失败")
	}

	if prototype != nil {
		return prototype.Number, nil
	}

	// 没找到样机，查询保卡表
	warranty, err := s.tabletControlRepo.QueryWarranty(c, barcode, number, imei)
	if err != nil {
		return "", errors.Wrap(err, "查询保卡信息失败")
	}

	if warranty != nil && warranty.Number != "" {
		return warranty.Number, nil
	}

	return "", errors.New("无效的条码或imei码或序列号")
}

// VerifyPhoneCodeForAllowUnknownApp 验证手机验证码
func (s *tabletControlSvc) VerifyPhoneCodeForAllowUnknownApp(c *gin.Context, req *entity.VerifyPhoneCodeForAllowUnknownAppRequest) error {
	if req.Phone == "" || req.Code == "" {
		return errors.New("参数错误")
	}

	// 验证手机验证码（30分钟有效期）
	result, err := s.tabletControlRepo.VerifyPhoneCodeFromDB(c, req.Code, req.Phone, 30)
	if err != nil {
		return errors.Wrap(err, "验证手机验证码失败")
	}

	if result == -1 || result == -2 {
		return errors.New("验证码不正确")
	}

	if result == 2 {
		return errors.New("验证码已过期")
	}

	return nil
}

// GetAllowNsfwUrl 获取设备NSFW URL允许状态
func (s *tabletControlSvc) GetAllowNsfwUrl(c *gin.Context, req *entity.GetAllowNsfwUrlRequest) (interface{}, error) {
	// 验证参数
	if req.Barcode == "" && req.Number == "" && req.IMEI == "" {
		return nil, errors.New("barcode、number、imei不能全部为空")
	}

	// 查找设备序列号
	deviceNumber, err := s.findDeviceNumber(c, req.Barcode, req.Number, req.IMEI)
	if err != nil {
		return nil, err
	}

	// 调用外部API获取NSFW URL状态
	data, message := s.apiClient.GetAllowNsfwUrl(c, deviceNumber)
	if data == nil && message != "success" {
		return nil, errors.New(message)
	}

	return data, nil
}

// SetAllowNsfwUrl 设置设备NSFW URL允许状态
func (s *tabletControlSvc) SetAllowNsfwUrl(c *gin.Context, req *entity.SetAllowNsfwUrlRequest) error {
	// 验证参数
	if req.Barcode == "" && req.Number == "" && req.IMEI == "" {
		return errors.New("barcode、number、imei不能全部为空")
	}
	if req.EndpointID == 0 {
		return errors.New("参数错误")
	}
	if req.Status != 0 && req.Status != 1 {
		return errors.New("状态值必须为0（禁止）或1（允许）")
	}

	// 查找设备序列号
	deviceNumber, err := s.findDeviceNumber(c, req.Barcode, req.Number, req.IMEI)
	if err != nil {
		return err
	}

	// 调用外部API设置NSFW URL状态
	success, message := s.apiClient.SetAllowNsfwUrl(c, deviceNumber, req.EndpointID, req.Status)
	if !success {
		return errors.New(message)
	}

	return nil
}

// CancelBindings 取消设备绑定
func (s *tabletControlSvc) CancelBindings(c *gin.Context, req *entity.CancelBindingsRequest) error {
	// 验证参数
	if req.Barcode == "" && req.Number == "" && req.IMEI == "" {
		return errors.New("参数错误")
	}

	var deviceNumber string

	// 查询prototype
	prototype, err := s.prototypeRepo.QueryPrototype(c, req.Barcode, req.Number, req.IMEI)
	if err != nil {
		return errors.Wrap(err, "查询样机信息失败")
	}

	if prototype != nil {
		// 如果找到prototype，直接使用其number
		deviceNumber = prototype.Number
	} else {
		// 没有找到prototype，查询warranty表
		var barcode = req.Barcode

		// 如果有IMEI，先通过IMEI获取barcode
		if req.IMEI != "" {
			warranty, err := s.tabletControlRepo.GetWarrantyByImei(c, req.IMEI)
			if err != nil {
				return errors.Wrap(err, "查询保卡信息失败")
			}
			if warranty == nil {
				return errors.New("imei码")
			}
			barcode = warranty.Barcode
		}

		// 使用barcode查询warranty
		if barcode != "" {
			warranty, err := s.tabletControlRepo.GetWarrantyByBarcode(c, barcode)
			if err != nil {
				return errors.Wrap(err, "查询保卡信息失败")
			}
			if warranty == nil {
				return errors.New("无保卡，无法解绑")
			}
			deviceNumber = warranty.Number
		} else {
			// 最后使用number查询warranty
			warranty, err := s.tabletControlRepo.GetWarrantyByNumber(c, req.Number)
			if err != nil {
				return errors.Wrap(err, "查询保卡信息失败")
			}
			if warranty == nil {
				return errors.New("无保卡，无法解绑")
			}
			deviceNumber = req.Number
		}
	}

	// 调用外部API取消绑定
	success, message := s.apiClient.CancelBindings(c, deviceNumber)
	if !success {
		return errors.New(message)
	}

	return nil
}

// UnbindWatch 解绑手表
func (s *tabletControlSvc) UnbindWatch(c *gin.Context, req *entity.UnbindWatchRequest) error {
	// 验证参数
	if req.Barcode == "" && req.Number == "" && req.IMEI == "" {
		return errors.New("barcode、number、imei不能全部为空")
	}

	var imei string

	// 首先查询prototype表
	prototype, err := s.prototypeRepo.QueryPrototype(c, req.Barcode, req.Number, req.IMEI)
	if err != nil {
		return errors.Wrap(err, "查询样机信息失败")
	}

	if prototype != nil {
		// 如果找到prototype，直接使用其IMEI
		imei = prototype.Imei
	} else {
		// 没有找到prototype，查询warranty表
		var warranty *dto.WarrantyWithEndpoint

		if req.IMEI != "" {
			warranty, err = s.tabletControlRepo.GetWarrantyByImei(c, req.IMEI)
			if err != nil {
				return errors.Wrap(err, "查询保卡信息失败")
			}
		} else if req.Barcode != "" {
			warranty, err = s.tabletControlRepo.GetWarrantyByBarcode(c, req.Barcode)
			if err != nil {
				return errors.Wrap(err, "查询保卡信息失败")
			}
		} else {
			warranty, err = s.tabletControlRepo.GetWarrantyByNumber(c, req.Number)
			if err != nil {
				return errors.Wrap(err, "查询保卡信息失败")
			}
		}

		if warranty == nil {
			return errors.New("无效的条码或imei码或序列号")
		}

		// 验证设备是否为手表
		if warranty.ModelID != 0 {
			machineInfo, err := s.machineRepo.GetMachineType(
				c,
				builder.NewMachineType().ModelIdEq(warranty.ModelID).VisibilityEq(1),
			)
			if err != nil {
				return errors.Wrap(err, "查询机型信息失败")
			}
			if machineInfo == nil || machineInfo.Series != 7 {
				return errors.New("此机器非手表")
			}
		}

		imei = warranty.Imei
	}

	if imei == "" {
		return errors.New("无保卡，无法解绑")
	}

	// 调用外部API解绑手表
	success, message := s.apiClient.UnbindWatch(c, imei, req.Username)
	if !success {
		return errors.New(message)
	}

	return nil
}
