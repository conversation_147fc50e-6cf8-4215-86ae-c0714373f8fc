package endpoint_convertor

import (
	"marketing-app/internal/handler/endpoint/dto"
	"marketing-app/internal/service/endpoint/entity"
)

type EndpointsConvertor struct{}

func (e *EndpointsConvertor) ClientToEntity(req *dto.EndpointsRequest) *entity.Endpoints {
	// 设置默认值
	if req.Type == 0 {
		req.Type = 1 // 默认售前
	}
	if req.Count <= 0 {
		req.Count = 10 // 默认10个
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	return &entity.Endpoints{
		Type:     req.Type,
		City:     req.City,
		District: req.District,
		Channel:  req.Channel,
		Lng:      req.Lng,
		Lat:      req.Lat,
		Count:    req.Count,
		Page:     req.Page,
	}
}
