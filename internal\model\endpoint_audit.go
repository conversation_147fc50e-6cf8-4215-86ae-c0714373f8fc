package model

import (
	"time"
)

// EndpointAudit 终端审核模型
type EndpointAudit struct {
	ID                 int        `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Endpoint           int        `gorm:"not null;column:endpoint" json:"endpoint" comment:"终端id"`
	PositionPriority   *string    `gorm:"type:varchar(100);column:position_priority" json:"position_priority,omitempty" comment:"终端位置,文字描述"`
	Area               *int       `gorm:"type:int(6);column:area" json:"area,omitempty" comment:"终端面积"`
	ChannelLevel       *int8      `gorm:"type:tinyint(1);column:channel_level" json:"channel_level,omitempty" comment:"渠道级别"`
	ChannelType        *int8      `gorm:"type:tinyint(4);column:channel_type" json:"channel_type,omitempty" comment:"渠道类别"`
	Sales              *int       `gorm:"type:int(10);column:sales" json:"sales,omitempty" comment:"销售额"`
	Images             *string    `gorm:"type:text;column:images" json:"images,omitempty" comment:"终端形象图片"`
	Attachment         *string    `gorm:"type:text;column:attachment" json:"attachment,omitempty" comment:"说明附件(报告文件信息点)"`
	Remark             *string    `gorm:"type:text;column:remark" json:"remark,omitempty" comment:"申请备注"`
	TopAgencyOpinion   *string    `gorm:"type:varchar(255);column:top_agency_opinion" json:"top_agency_opinion,omitempty" comment:"审核描述意见"`
	Lat                string     `gorm:"type:varchar(255);default:'0.000000';column:lat" json:"lat" comment:"纬度（高德坐标）"`
	Lng                string     `gorm:"type:varchar(20);default:'0.000000';column:lng" json:"lng" comment:"经度（高德坐标）"`
	Status             int8       `gorm:"type:tinyint(1);default:0;column:status" json:"status" comment:"总代审核情况,0是未审核,1是总代审核通过,2是公司管理员审核经过,-1是总代审核不通过,2是公司管理审核通过,-2是公司审核不通过"`
	IsAuditAgain       int8       `gorm:"type:tinyint(1);not null;default:0;column:is_audit_again" json:"is_audit_again" comment:"总代是否有再次修改审核内容,一般是公司管理员审核不通过总代再次修改审核之后就会设为1"`
	TopAgencyAuditedAt *time.Time `gorm:"column:top_agency_audited_at" json:"top_agency_audited_at,omitempty" comment:"总代审核时间"`
	ManagerAuditedAt   *time.Time `gorm:"column:manager_audited_at" json:"manager_audited_at,omitempty" comment:"公司管理员审核时间"`
	CreatedAt          time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	UpdatedAt          *time.Time `gorm:"column:updated_at" json:"updated_at,omitempty"`
	ManagerOpinion     *string    `gorm:"type:varchar(255);column:manager_opinion" json:"manager_opinion,omitempty" comment:"审核描述意见"`
}

// TableName 指定表名
func (EndpointAudit) TableName() string {
	return "endpoint_audit"
}
