package handler

import (
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type PromotionHandler interface {
	GetList(c *gin.Context)
	GetDetail(c *gin.Context)
	UploadReceipt(c *gin.Context)
	GetPromotions(c *gin.Context)
}

type promotionHandler struct {
	service service.PromotionService
	userSvc service.AdminUserService
}

func NewPromotionListHandler(service service.PromotionService, userSvc service.AdminUserService) PromotionHandler {
	return &promotionHandler{
		service: service,
		userSvc: userSvc,
	}
}

// GetList 获取促销活动列表
func (h *promotionHandler) GetList(c *gin.Context) {
	var req dto.PromotionListsReq
	var err error

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		ResponseError(c, err)
		return
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	// 获取用户信息
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	userEndpoint, err := h.userSvc.GetUserEndpoint(c, uid)
	if err != nil {
		ResponseError(c, err)
		return
	}
	req.Endpoint = userEndpoint.ID

	// 调用服务
	data, err := h.service.GetList(c, &req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, data)
}

// GetDetail 获取促销活动详情
func (h *promotionHandler) GetDetail(c *gin.Context) {
	var req dto.PromotionListDetailReq
	if err := c.ShouldBindUri(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 获取用户信息
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	userEndpoint, err := h.userSvc.GetUserEndpoint(c, uid)
	if err != nil {
		ResponseError(c, err)
		return
	}
	req.Endpoint = userEndpoint.ID
	// 调用服务
	data, err := h.service.GetDetail(c, &req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, data)
}

// UploadReceipt 上传回执
func (h *promotionHandler) UploadReceipt(c *gin.Context) {
	var req dto.ReceiptUploadReq
	var err error

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 绑定JSON参数
	var jsonReq struct {
		Receipt string `form:"receipt" json:"receipt" binding:"required"`
	}
	if err = c.ShouldBindJSON(&jsonReq); err != nil {
		ResponseError(c, err)
		return
	}
	req.Receipt = jsonReq.Receipt

	// 获取用户信息
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	userEndpoint, err := h.userSvc.GetUserEndpoint(c, uid)
	if err != nil {
		ResponseError(c, err)
		return
	}

	// 调用服务
	err = h.service.UploadReceipt(c, &req, userEndpoint.ID)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, dto.ReceiptUploadResp{
		Message: "上传成功",
	})
}

// GetPromotions 获取促销活动列表
func (h *promotionHandler) GetPromotions(c *gin.Context) {
	var req dto.PromotionsReq
	var err error

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		ResponseError(c, err)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	// 获取用户信息
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	userEndpoint, err := h.userSvc.GetUserEndpoint(c, uid)
	if err != nil {
		ResponseError(c, err)
		return
	}
	req.Endpoint = userEndpoint.ID

	// 调用服务
	data, err := h.service.GetPromotions(c, &req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, data)
}

// UserInfo 用户信息结构
type UserInfo struct {
	Agency    int  `json:"agency"`
	Endpoint  int  `json:"endpoint"`
	IsCompany bool `json:"is_company"`
}
