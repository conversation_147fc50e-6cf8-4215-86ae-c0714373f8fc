package prototype_manage

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/prototype/prototype_manage/dto"
	"marketing-app/internal/pkg/convertor/prototype_convertor"
	"marketing-app/internal/router/prototype/prototype_manage/client"
	service "marketing-app/internal/service/prototype/prototype_manage"
)

type Prototype interface {
	CreatePrototype(c *gin.Context)
	ListPrototype(c *gin.Context)
	DeletePrototype(c *gin.Context)
	CheckPrototype(c *gin.Context)
	GetPrototypeLimit(c *gin.Context)
	GetPrototypeUserList(c *gin.Context)
	BindPrototypeUser(c *gin.Context)
	ChangePrototypeUserPhone(c *gin.Context)
	DeletePrototypeUser(c *gin.Context)
	PrototypeWarrantyQuery(c *gin.Context)
}

type prototype struct {
	prototypeSvc service.PrototypeSvc
}

func NewPrototypeHandler(svc service.PrototypeSvc) Prototype {
	return &prototype{prototypeSvc: svc}
}

func (p *prototype) CreatePrototype(c *gin.Context) {
	var (
		req  client.CreatePrototypeRequest
		err  error
		resp dto.CreatePrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	req.Uid = int(uid)

	err = p.prototypeSvc.CreatePrototype(
		c,
		prototype_convertor.NewPrototypeCreateConvertor().ClientToEntity(&req),
	)
	resp = dto.CreatePrototypeResp{
		Message: "添加成功！",
	}
}

func (p *prototype) ListPrototype(c *gin.Context) {
	var (
		req  client.ListPrototypeRequest
		err  error
		resp *dto.PrototypeListResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	modelCategory, data, err := p.prototypeSvc.GetPrototypeListData(
		c,
		prototype_convertor.NewPrototypeListConvertor().ClientToEntity(&req),
	)
	var categoryList []map[string]interface{}
	for _, category := range modelCategory {
		categoryList = append(categoryList, map[string]interface{}{
			"id":   category.ID,
			"name": category.Name,
		})
	}
	resp = &dto.PrototypeListResp{
		Data:          nil,
		Size:          0,
		IsEnd:         false,
		ModelCategory: categoryList,
	}
	if data != nil {
		resp.Data = data.Data
		resp.Size = data.Size
		resp.IsEnd = data.IsEnd
	}
	if resp.Data == nil {
		err = errors.New("未找到数据")
		return
	}
}

func (p *prototype) DeletePrototype(c *gin.Context) {
	var (
		req  client.DeletePrototypeRequest
		err  error
		resp *dto.DeletePrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if req.Barcode == "" || req.EndpointID == 0 {
		err = errors.New("参数错误")
		return
	}
	msg, err := p.prototypeSvc.DeletePrototype(
		c,
		prototype_convertor.NewPrototypeDeleteConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = &dto.DeletePrototypeResp{
		Message: msg,
	}
}

func (p *prototype) CheckPrototype(c *gin.Context) {
	var (
		req  client.CheckPrototypeRequest
		err  error
		resp *dto.CheckPrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	if req.Barcode == "" {
		err = errors.New("参数错误")
		return
	}

	model, barcode, err := p.prototypeSvc.CheckPrototype(
		c,
		prototype_convertor.NewPrototypeCheckConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	resp = &dto.CheckPrototypeResp{
		Model:   model,
		Barcode: barcode,
	}
}

// GetPrototypeLimit 获取代理商样机限制信息
func (p *prototype) GetPrototypeLimit(c *gin.Context) {
	var (
		err  error
		resp *dto.PrototypeLimitResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 调用服务层获取样机限制信息
	r, err := p.prototypeSvc.GetPrototypeLimit(c, int(uid))
	if err != nil {
		return
	}
	resp = &dto.PrototypeLimitResponse{
		PrototypeLimit:          r.PrototypeLimit,
		PrototypeFrequencyLimit: r.PrototypeFrequencyLimit,
		PrototypeTotal:          r.PrototypeTotal,
		PrototypeMonthTotal:     r.PrototypeMonthTotal,
	}
}

// GetPrototypeUserList 获取代理商演示用户列表
func (p *prototype) GetPrototypeUserList(c *gin.Context) {
	var (
		err  error
		resp *dto.PrototypeUserListResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 调用服务层获取演示用户列表
	r, err := p.prototypeSvc.GetPrototypeUserList(c, int(uid))
	if err != nil {
		return
	}
	// 转换用户列表数据
	var userList []dto.PrototypeUserInfo
	for _, user := range r.UserList {
		userList = append(userList, dto.PrototypeUserInfo{
			ID:    user.ID,
			Phone: user.Phone,
		})
	}

	resp = &dto.PrototypeUserListResponse{
		UserList:  userList,
		UserLimit: r.UserLimit,
	}
}

// BindPrototypeUser 样机演示账号绑定
func (p *prototype) BindPrototypeUser(c *gin.Context) {
	var (
		req  dto.PrototypeUserBindRequest
		err  error
		resp *dto.PrototypeUserBindResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层进行用户绑定
	r, err := p.prototypeSvc.BindPrototypeUser(c, req.Phone, req.Code, req.EndpointID)
	if err != nil {
		return
	}

	// 将repository层的dto转换为handler层的dto
	resp = &dto.PrototypeUserBindResponse{
		ID:    r.ID,
		Phone: r.Phone,
	}
}

// ChangePrototypeUserPhone 终端演示用户修改手机号
func (p *prototype) ChangePrototypeUserPhone(c *gin.Context) {
	var (
		req  dto.PrototypeUserChangeRequest
		err  error
		resp *dto.PrototypeUserChangeResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层进行手机号修改
	err = p.prototypeSvc.ChangePrototypeUserPhone(c, req.EndpointID, req.Code, req.OldPhone, req.NewPhone)
	if err != nil {
		return
	}

	resp = &dto.PrototypeUserChangeResponse{
		Success: true,
	}
}

// DeletePrototypeUser 终端演示用户删除
func (p *prototype) DeletePrototypeUser(c *gin.Context) {
	var (
		req  dto.PrototypeUserDeleteRequest
		err  error
		resp *dto.PrototypeUserDeleteResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层进行用户删除
	err = p.prototypeSvc.DeletePrototypeUser(c, req.EndpointID, req.Phone)
	if err != nil {
		return
	}

	resp = &dto.PrototypeUserDeleteResponse{
		Success: true,
	}
}

// PrototypeWarrantyQuery 查询样机是否有保卡或者已经入样机库
func (p *prototype) PrototypeWarrantyQuery(c *gin.Context) {
	var (
		req  dto.PrototypeWarrantyQueryRequest
		err  error
		resp *dto.PrototypeWarrantyQueryResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定GET请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 调用服务层查询样机保卡信息
	data, err := p.prototypeSvc.PrototypeWarrantyQuery(c, req.Barcode, req.Number, req.IMEI)
	if err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.PrototypeWarrantyQueryResponse{
		Data: data,
	}
}
