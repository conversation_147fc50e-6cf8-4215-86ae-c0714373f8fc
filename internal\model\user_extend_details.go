package model

import "time"

// UserExtendDetails 用户扩展信息模型
type UserExtendDetails struct {
	UserID        uint      `gorm:"column:user_id;primaryKey;type:int(10) unsigned;not null;comment:'用户ID，关联admin_users表'" json:"user_id"`
	Name          *string   `gorm:"column:name;type:varchar(255);comment:'昵称'" json:"name,omitempty"`
	Phone         *string   `gorm:"column:phone;type:varchar(100);comment:'电话号码'" json:"phone,omitempty"`
	WechatID      *string   `gorm:"column:wechat_id;type:varchar(100);comment:'微信号'" json:"wechat_id,omitempty"`
	WechatImage   *string   `gorm:"column:wechat_image;type:text;comment:'微信二维码图片路径'" json:"wechat_image,omitempty"`
	EndpointID    uint      `gorm:"column:endpoint_id;type:int(11) unsigned;not null;comment:'所属终端的id'" json:"endpoint_id"`
	CreatedAt     time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'创建时间戳'" json:"created_at"`
	DefaultUser   *uint8    `gorm:"column:default_user;type:tinyint(2) unsigned;comment:'默认导购标识（1-默认）'" json:"default_user,omitempty"`
	QrCodeAddress *string   `gorm:"column:qr_code_address;type:varchar(255);comment:'微信二维码地址'" json:"qr_code_address,omitempty"`
	PadID         *string   `gorm:"column:pad_id;type:varchar(100);comment:'绑定平板id'" json:"pad_id,omitempty"`
}

// TableName 指定表名
func (UserExtendDetails) TableName() string {
	return "user_extend_details"
}
