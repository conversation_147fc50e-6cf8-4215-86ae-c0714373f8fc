package dto

import (
	"database/sql/driver"
	"time"
)

// NullTime represents a nullable time.Time
type NullTime struct {
	Time  time.Time
	Valid bool
}

func (nt *NullTime) Scan(value interface{}) error {
	if value == nil {
		nt.Valid = false
		return nil
	}
	nt.Valid = true
	switch v := value.(type) {
	case time.Time:
		nt.Time = v
	default:
		nt.Valid = false
	}
	return nil
}

func (nt NullTime) Value() (driver.Value, error) {
	if !nt.Valid {
		return nil, nil
	}
	return nt.Time, nil
}

// CustomerListItem 客户列表项
type CustomerListItem struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Phone       string    `json:"phone"`
	Email       string    `json:"email"`
	Company     string    `json:"company"`
	Position    string    `json:"position"`
	Region      string    `json:"region"`
	Industry    string    `json:"industry"`
	Source      string    `json:"source"`
	Level       uint8     `json:"level"`
	Status      uint8     `json:"status"`
	Remark      string    `json:"remark"`
	CreatedBy   uint      `json:"created_by"`
	UpdatedBy   uint      `json:"updated_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	LastContact *NullTime `json:"last_contact,omitempty"`
}

// CustomerListResponse 客户列表响应
type CustomerListResponse struct {
	Data  []CustomerListItem `json:"data"`
	Size  int                `json:"size"`
	IsEnd bool               `json:"isEnd"`
}

// CustomerDetailItem 客户详情项
type CustomerDetailItem struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Phone       string    `json:"phone"`
	Email       string    `json:"email"`
	Company     string    `json:"company"`
	Position    string    `json:"position"`
	Region      string    `json:"region"`
	Industry    string    `json:"industry"`
	Source      string    `json:"source"`
	Level       uint8     `json:"level"`
	Status      uint8     `json:"status"`
	Remark      string    `json:"remark"`
	CreatedBy   uint      `json:"created_by"`
	UpdatedBy   uint      `json:"updated_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	LastContact *NullTime `json:"last_contact,omitempty"`
}

// CustomerContactItem 客户联系记录项
type CustomerContactItem struct {
	ID         uint      `json:"id"`
	CustomerID uint      `json:"customer_id"`
	Type       uint8     `json:"type"`
	Content    string    `json:"content"`
	Result     string    `json:"result"`
	NextTime   *NullTime `json:"next_time,omitempty"`
	CreatedBy  uint      `json:"created_by"`
	CreatedAt  time.Time `json:"created_at"`
}

// CustomerContactListResponse 客户联系记录列表响应
type CustomerContactListResponse struct {
	Data  []CustomerContactItem `json:"data"`
	Size  int                   `json:"size"`
	IsEnd bool                  `json:"isEnd"`
}

// CustomerFollowItem 客户跟进记录项
type CustomerFollowItem struct {
	ID         uint      `json:"id"`
	CustomerID uint      `json:"customer_id"`
	Stage      uint8     `json:"stage"`
	Priority   uint8     `json:"priority"`
	Progress   uint8     `json:"progress"`
	Content    string    `json:"content"`
	CreatedBy  uint      `json:"created_by"`
	CreatedAt  time.Time `json:"created_at"`
}

// CustomerFollowListResponse 客户跟进记录列表响应
type CustomerFollowListResponse struct {
	Data  []CustomerFollowItem `json:"data"`
	Size  int                  `json:"size"`
	IsEnd bool                 `json:"isEnd"`
}

// CustomerSearchItem 客户搜索项
type CustomerSearchItem struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Phone       string    `json:"phone"`
	Email       string    `json:"email"`
	Company     string    `json:"company"`
	Position    string    `json:"position"`
	Region      string    `json:"region"`
	Industry    string    `json:"industry"`
	Source      string    `json:"source"`
	Level       uint8     `json:"level"`
	Status      uint8     `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	LastContact *NullTime `json:"last_contact,omitempty"`
}

// CustomerStatisticsItem 客户统计项
type CustomerStatisticsItem struct {
	TotalCount     int `json:"total_count"`
	ActiveCount    int `json:"active_count"`
	InactiveCount  int `json:"inactive_count"`
	HighLevelCount int `json:"high_level_count"`
	MidLevelCount  int `json:"mid_level_count"`
	LowLevelCount  int `json:"low_level_count"`
}
