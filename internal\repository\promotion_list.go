package repository

import (
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/model"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PromotionListRepository 促销列表仓储接口
type PromotionListRepository interface {
	GetList(c *gin.Context, param *dto.PromotionListsReq) ([]dto.PromotionListItem, int64, error)
	GetDetail(c *gin.Context, id int) (*dto.PromotionListDetailResp, error)
	UpdateReceipt(c *gin.Context, id int, receipt string) error
	GetPromotionDetail(c *gin.Context, id int) (*model.SalesPromotion, error)
	GetReceiptsByListIds(c *gin.Context, ids []int) ([]dto.PromotionListReceiptResp, error)
	GetWarrantyInfoByIds(c *gin.Context, warrantyIds []int) (map[int]WarrantyInfo, error)
	GetPromotions(c *gin.Context, param *dto.PromotionsReq) ([]dto.PromotionItem, int64, error)
	GetPromotionsCounts(c *gin.Context, ids []int, param *dto.PromotionsReq) ([]dto.PromotionsCount, []dto.PromotionsCount, error)
}

// WarrantyInfo 保修信息
type WarrantyInfo struct {
	Id             int    `json:"id"`
	Number         string `json:"number"`
	ActivatedAtOld string `json:"activated_at_old"`
	StudentName    string `json:"student_name"`
}

type promotionListRepository struct {
	db *gorm.DB
}

func NewPromotionListRepository(db *gorm.DB) PromotionListRepository {
	return &promotionListRepository{
		db: db,
	}
}

func (r *promotionListRepository) GetList(c *gin.Context, param *dto.PromotionListsReq) ([]dto.PromotionListItem, int64, error) {
	var result []dto.PromotionListItem
	var total int64

	query := r.db.WithContext(c).Table("sales_promotion_list l").
		Select(`l.*, 
			CONCAT(rp.region_name, rc.region_name) as region_name,
			a.name as agency_name,
			e.name as endpoint_name,
			e.code as endpoint_code,
			e.manager,
			e.phone as endpoint_phone,
			e.address`).
		Joins("LEFT JOIN endpoint e ON l.endpoint = e.id").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Joins("LEFT JOIN region rp ON e.province = rp.region_id").
		Joins("LEFT JOIN region rc ON e.city = rc.region_id").
		Joins("LEFT JOIN warranty_return wr ON l.warranty_id = wr.warranty_id").
		Where("l.sales_promotion_id = ?", param.Id).
		Where("wr.id IS NULL")

	// 添加筛选条件
	if param.Agency > 0 {
		query = query.Where("e.top_agency = ?", param.Agency)
	}
	if param.Endpoint > 0 {
		query = query.Where("l.endpoint = ?", param.Endpoint)
	}
	if param.Receipt > 0 {
		if param.Receipt == 1 {
			query = query.Where("l.is_receipt = 1")
		} else if param.Receipt == 2 {
			query = query.Where("l.is_receipt = 0")
		}
	}
	if len(param.ModelId) > 0 {
		query = query.Where("l.model_id IN ?", param.ModelId)
	}
	if param.Keyword != "" {
		query = query.Where("l.barcode LIKE ?", "%"+param.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (param.Page - 1) * param.PageSize
	if err := query.Order("l.buy_date").
		Offset(offset).
		Limit(param.PageSize).
		Find(&result).Error; err != nil {
		return nil, 0, err
	}

	return result, total, nil
}

func (r *promotionListRepository) GetDetail(c *gin.Context, id int) (*dto.PromotionListDetailResp, error) {
	var result dto.PromotionListDetailResp

	err := r.db.WithContext(c).Table("sales_promotion_list l").
		Select(`l.*,
			w.student_uid,
			w.student_name,
			a.name as agency_name,
			w.activated_at_old as activated_at,
			w.number,
			TIMESTAMPDIFF(HOUR, w.buy_date, w.activated_at_old) as hour_interval,
			e.code as endpoint_code,
			e.name as endpoint_name,
			e.manager,
			wr.return_at`).
		Joins("LEFT JOIN warranty w ON l.warranty_id = w.id").
		Joins("LEFT JOIN endpoint e ON e.id = w.endpoint").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Joins("LEFT JOIN region rp ON e.province = rp.region_id").
		Joins("LEFT JOIN region rc ON e.city = rc.region_id").
		Joins("LEFT JOIN warranty_return wr ON w.barcode = wr.barcode").
		Where("l.id = ?", id).
		Group("l.barcode").
		First(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *promotionListRepository) UpdateReceipt(c *gin.Context, id int, receipt string) error {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新促销列表
		if err := tx.Model(&model.SalesPromotionList{}).
			Where("id = ?", id).
			Updates(map[string]interface{}{
				"receipt_at": gorm.Expr("NOW()"),
				"is_receipt": 1,
			}).Error; err != nil {
			return err
		}

		// 删除旧的回执
		if err := tx.Where("sales_promotion_list_id = ? AND type = 1", id).
			Delete(&model.SalesPromotionListReceipt{}).Error; err != nil {
			return err
		}

		// 插入新的回执
		receiptSlice := strings.Split(receipt, ",")
		var receipts []model.SalesPromotionListReceipt
		for _, item := range receiptSlice {
			// 处理number
			number := ""
			parts := strings.Split(item, "/")
			if len(parts) > 0 {
				lastPart := parts[len(parts)-1]
				subParts := strings.Split(lastPart, "_")
				if len(subParts) > 0 {
					number = subParts[0]
				}
			}

			receipts = append(receipts, model.SalesPromotionListReceipt{
				SalesPromotionListId: id,
				Receipt:              item,
				Number:               number,
				Type:                 1,
			})
		}

		if err := tx.Create(&receipts).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *promotionListRepository) GetPromotionDetail(c *gin.Context, id int) (*model.SalesPromotion, error) {
	var promotion model.SalesPromotion
	err := r.db.WithContext(c).Where("id = ?", id).First(&promotion).Error
	return &promotion, err
}

func (r *promotionListRepository) GetReceiptsByListIds(c *gin.Context, ids []int) ([]dto.PromotionListReceiptResp, error) {
	var receipts []dto.PromotionListReceiptResp

	if len(ids) == 0 {
		return receipts, nil
	}

	// 终端模式：返回合并的回执字符串
	err := r.db.WithContext(c).Model(&model.SalesPromotionListReceipt{}).
		Select("sales_promotion_list_id, GROUP_CONCAT(receipt) AS receipt").
		Where("sales_promotion_list_id IN ?", ids).
		Group("sales_promotion_list_id").
		Find(&receipts).Error
	return receipts, err
}

func (r *promotionListRepository) GetWarrantyInfoByIds(c *gin.Context, warrantyIds []int) (map[int]WarrantyInfo, error) {
	var warranties []WarrantyInfo
	err := r.db.WithContext(c).Table("warranty").
		Select("id, number, activated_at_old, student_name").
		Where("id IN ?", warrantyIds).
		Find(&warranties).Error

	if err != nil {
		return nil, err
	}

	// 转换为map
	warrantyMap := make(map[int]WarrantyInfo)
	for _, w := range warranties {
		warrantyMap[w.Id] = w
	}

	return warrantyMap, nil
}

func (r *promotionListRepository) GetPromotions(c *gin.Context, param *dto.PromotionsReq) ([]dto.PromotionItem, int64, error) {
	var result []dto.PromotionItem
	var total int64

	query := r.db.WithContext(c).Table("sales_promotion")

	// 添加筛选条件
	if param.Type > 0 {
		query = query.Where("type = ?", param.Type)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (param.Page - 1) * param.PageSize
	if err := query.Order("id desc").
		Offset(offset).
		Limit(param.PageSize).
		Find(&result).Error; err != nil {
		return nil, 0, err
	}

	return result, total, nil
}

func (r *promotionListRepository) GetPromotionsCounts(c *gin.Context, ids []int, param *dto.PromotionsReq) ([]dto.PromotionsCount, []dto.PromotionsCount, error) {
	var listCount, receiptCount []dto.PromotionsCount

	if len(ids) == 0 {
		return listCount, receiptCount, nil
	}

	// 构建基础查询
	baseQuery := r.db.WithContext(c).Table("sales_promotion_list l").
		Where("l.sales_promotion_id IN ?", ids)

	// 如果需要按代理商或终端筛选，添加JOIN
	if param.Agency > 0 || param.Endpoint > 0 {
		baseQuery = baseQuery.Joins("LEFT JOIN endpoint e ON l.endpoint = e.id")
	}

	// 添加筛选条件
	if param.Agency > 0 {
		baseQuery = baseQuery.Where("e.top_agency = ?", param.Agency)
	}
	if param.Endpoint > 0 {
		baseQuery = baseQuery.Where("l.endpoint = ?", param.Endpoint)
	}

	// 查询活动人数（排除退货的）
	listQuery := baseQuery.
		Joins("LEFT JOIN warranty_return wr ON l.warranty_id = wr.warranty_id").
		Where("wr.id IS NULL").
		Select("sales_promotion_id as id, COUNT(*) as list_count").
		Group("sales_promotion_id")

	if err := listQuery.Find(&listCount).Error; err != nil {
		return nil, nil, err
	}

	// 查询回执人数
	receiptQuery := r.db.WithContext(c).Table("sales_promotion_list l").
		Where("l.sales_promotion_id IN ?", ids).
		Where("l.is_receipt = 1")

	// 如果需要按代理商或终端筛选，添加JOIN
	if param.Agency > 0 || param.Endpoint > 0 {
		receiptQuery = receiptQuery.Joins("LEFT JOIN endpoint e ON l.endpoint = e.id")
	}

	// 添加筛选条件
	if param.Agency > 0 {
		receiptQuery = receiptQuery.Where("e.top_agency = ?", param.Agency)
	}
	if param.Endpoint > 0 {
		receiptQuery = receiptQuery.Where("l.endpoint = ?", param.Endpoint)
	}

	receiptQuery = receiptQuery.
		Select("sales_promotion_id as id, COUNT(*) as receipt_count").
		Group("sales_promotion_id")

	if err := receiptQuery.Find(&receiptCount).Error; err != nil {
		return nil, nil, err
	}

	return listCount, receiptCount, nil
}
