package prototype_manage

import (
	"fmt"
	"marketing-app/internal/api/client/binding"
	"marketing-app/internal/api/client/binding/config"
	"marketing-app/internal/cache"
	"marketing-app/internal/consts"
	"marketing-app/internal/pkg/utils"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	prototypeRepo "marketing-app/internal/repository/prototype"
	prototypeBuilder "marketing-app/internal/repository/prototype/builder"
	"marketing-app/internal/repository/prototype/dto"
	userRepo "marketing-app/internal/repository/user"
	warrantyBaseRepo "marketing-app/internal/repository/warranty/base"
	"marketing-app/internal/repository/warranty/base/builder"
	warrantyBaseDto "marketing-app/internal/repository/warranty/base/dto"
	"marketing-app/internal/service/prototype/prototype_manage/entity"
	"strconv"

	"github.com/pkg/errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PrototypeSvc interface {
	CreatePrototype(c *gin.Context, param *entity.Prototype) (err error)
	GetPrototypeListData(c *gin.Context, param *entity.ListPrototype) ([]dto.ModelCategory, *dto.PrototypeListResult, error)
	DeletePrototype(c *gin.Context, param *entity.DeletePrototype) (msg string, err error)
	CheckPrototype(c *gin.Context, param *entity.CheckPrototype) (model string, barcode string, err error)
	GetPrototypeLimit(c *gin.Context, uid int) (*dto.PrototypeLimitResponse, error)
	GetPrototypeUserList(c *gin.Context, uid int) (*dto.PrototypeUserListResponse, error)
	BindPrototypeUser(c *gin.Context, phone, code string, endpointID int) (*dto.PrototypeUserBind, error)
	ChangePrototypeUserPhone(c *gin.Context, endpointID int, code, oldPhone, newPhone string) error
	DeletePrototypeUser(c *gin.Context, endpointID int, phone string) error
	PrototypeWarrantyQuery(c *gin.Context, barcode, number, imei string) (interface{}, error)
}

type prototype struct {
	repo           prototypeRepo.Prototype
	warrantyRepo   warrantyBaseRepo.Warranty
	endpointRepo   endpointRepo.Endpoint
	drpMachineRepo machineRepo.Machine
	prototypeCache cache.PrototypeCache
	bindingClient  binding.BindingClient
	userRepo       userRepo.User
}

func NewPrototype(
	repo prototypeRepo.Prototype,
	warrantyRepo warrantyBaseRepo.Warranty,
	endpointRepo endpointRepo.Endpoint,
	drpMachineRepo machineRepo.Machine,
	prototypeCache cache.PrototypeCache,
	userRepo userRepo.User,
) PrototypeSvc {
	cfg := config.LoadBindingConfig()
	bindingClient := binding.NewBindingClient(cfg)

	return &prototype{
		repo:           repo,
		warrantyRepo:   warrantyRepo,
		endpointRepo:   endpointRepo,
		drpMachineRepo: drpMachineRepo,
		prototypeCache: prototypeCache,
		bindingClient:  bindingClient,
		userRepo:       userRepo,
	}
}

func (p *prototype) CreatePrototype(c *gin.Context, param *entity.Prototype) (err error) {
	warranty, err := p.checkWarranty(c, param.Barcode)
	if err != nil {
		return
	}
	endpointAgencyInfo, err := p.endpointRepo.GetEndpointAgency(c, param.EndpointId)
	if err != nil {
		return
	}
	prototypeInfo, err := p.checkPrototypeEntry(c, param.Barcode, param.EndpointId, endpointAgencyInfo.TopAgency)
	if err != nil {
		return
	}
	// 退货机器都不能录入样机
	warrantyReturns, err := p.repo.GetWarrantyReturns(c, param.Barcode)
	if err != nil {
		return
	}
	if len(warrantyReturns) > 0 && warrantyReturns[0].ReturnAt != nil {
		err = errors.New("此机器已经被退过机，无法设置为样机")
		return
	}
	if prototypeInfo == nil {
		if warranty.Id != 0 && warranty.Status == 0 && warranty.ActivatedAtOld != nil && warranty.CategoryId == 1 {
			err = errors.New("此机器已经被激活，无法设置为样机")
			return
		}
	}
	if prototypeInfo != nil && prototypeInfo.Type == 0 && prototypeInfo.CategoryID == 1 {
		if warranty.Id != 0 && warranty.Status == 0 && warranty.ActivatedAtOld != nil && warranty.CategoryId == 1 {
			err = errors.New("此机器已经被激活，无法设置为样机")
			return
		}
	}
	// 样机限制
	limit := 20
	limitInfo, _ := p.repo.GetPrototypeLimit(c, param.EndpointId)
	if limitInfo != nil && limitInfo.PrototypeLimit != nil && limitInfo.PrototypeFrequencyLimit != nil {
		if *limitInfo.PrototypeLimit != -1 {
			limit = *limitInfo.PrototypeLimit
		}
	}
	total, _, err := p.repo.GetPrototypeCount(c, param.EndpointId)
	if err != nil {
		err = errors.New("系统错误")
		return
	}
	if total >= int64(limit) {
		err = fmt.Errorf("在库样机数已到达最大限制: %d 台", limit)
		return
	}
	// MES 机器信息
	mes, err := utils.CheckMachine(utils.CheckMachineParams{
		Barcode: param.Barcode,
	})
	if err != nil || mes == nil {
		err = errors.New("此条码不可作为样机")
		return
	}
	// 机型可做样机校验
	modelID, _ := strconv.Atoi(mes["model_id"].(string))
	ok, err := p.repo.ModelCanBePrototype(c, modelID)
	if err != nil || !ok {
		err = errors.New("此机型不可作为样机")
		return
	}
	// 新增样机
	err = p.repo.CreateTransaction(c, param.Barcode, param.EndpointId, param.Uid, prototypeInfo, mes, p.drpMachineRepo)
	if err != nil {
		err = errors.New("系统错误")
		return
	}
	// 设置缓存
	_ = p.prototypeCache.Set(c, mes["number"].(string))
	// 同步进销存
	err = p.drpMachineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(param.Barcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(4).
			ReturnEq(0),
	)
	if err != nil {
		err = errors.New("系统错误")
		return
	}
	return nil
}

func (p *prototype) GetPrototypeListData(c *gin.Context, param *entity.ListPrototype) ([]dto.ModelCategory, *dto.PrototypeListResult, error) {
	// 获取机型分类
	modelCategory, err := p.repo.GetPrototypeMachineCategory(c)
	if err != nil {
		return nil, nil, err
	}
	// 获取样机列表
	data, err := p.repo.GetPrototypeList(c, param.EndpointId, param.PageSize, param.Page)
	if err != nil {
		return nil, nil, err
	}

	return modelCategory, data, nil
}

func (p *prototype) DeletePrototype(c *gin.Context, param *entity.DeletePrototype) (msg string, err error) {
	// 获取样机信息
	prototypeInfo, err := p.repo.GetPrototypeInfo(c, param.Barcode)
	if err != nil {
		return
	}
	if prototypeInfo == nil {
		err = errors.New("此机器非样机，无需离库")
		return
	}
	// 权限验证
	if prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.Discontinued == 0 {
		err = errors.New("演示样机，无权操作")
		return
	}
	if prototypeInfo.Endpoint != param.EndpointId {
		err = errors.New("非此终端机器，无权操作")
		return
	}
	// 删除样机（离库）
	_, msg, err = p.repo.DeletePrototype(c, param.Barcode, param.EndpointId)
	if err != nil {
		err = errors.Wrap(err, "删除样机失败")
		return
	}
	// 删除缓存
	err = p.prototypeCache.Del(c, prototypeInfo.Number)
	if err != nil {
		err = errors.New("删除样机成功，更新缓存失败")
	}
	// 如果是教育类设备则需要取消绑定
	if prototypeInfo.CategoryID == consts.ModelCategoryStudentPad {
		success, err := p.bindingClient.CancelBinding(c, prototypeInfo.Number)
		if err != nil {
			return "系统错误", err
		}
		if success {
			msg = "样机离库成功！此设备的家长助手绑定信息和使用记录已被重置"
		}
	}
	//同步进销存
	err = p.drpMachineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(param.Barcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(5).
			ReturnEq(0),
	)
	if err != nil {
		err = errors.New("同步进销存失败")
		return
	}

	return msg, nil
}

func (p *prototype) CheckPrototype(c *gin.Context, param *entity.CheckPrototype) (model string, barcode string, err error) {
	// 检查保卡
	warranty, err := p.checkWarranty(c, param.Barcode)
	if err != nil {
		return "", "", err
	}

	// 获取样机信息
	prototypeInfo, err := p.repo.GetPrototypeInfo(c, param.Barcode)
	if err != nil {
		return "", "", err
	}

	// 退货机器都不能录入样机
	warrantyReturns, err := p.repo.GetWarrantyReturns(c, param.Barcode)
	if err != nil {
		return "", "", err
	}
	if len(warrantyReturns) > 0 && warrantyReturns[0].ReturnAt != nil {
		err = errors.New("此机器已经被退过机，无法设置为样机")
		return "", "", err
	}

	if prototypeInfo == nil {
		if warranty.Id != 0 && warranty.Status == 0 && warranty.ActivatedAtOld != nil && warranty.CategoryId == 1 {
			err = errors.New("此机器已经被激活，无法设置为样机")
			return "", "", err
		}
	}
	// 如果不是政策样机 检测激活时间
	if prototypeInfo != nil && prototypeInfo.Type == 0 && prototypeInfo.CategoryID == 1 {
		if warranty.Id != 0 && warranty.Status == 0 && warranty.ActivatedAtOld != nil && warranty.CategoryId == 1 {
			err = errors.New("此机器已经被激活，无法设置为样机")
			return "", "", err
		}
	}

	// 查MES
	mes, err := utils.CheckMachine(utils.CheckMachineParams{
		Barcode: param.Barcode,
	})
	if err != nil || mes == nil {
		err = errors.New("此条码不可作为样机")
		return "", "", err
	}

	// 机型校验
	modelID, _ := strconv.Atoi(mes["model_id"].(string))
	ok, err := p.repo.ModelCanBePrototype(c, modelID)
	if err != nil || !ok {
		err = errors.New("此机型不可作为样机")
		return "", "", err
	}

	return mes["model"].(string), param.Barcode, nil
}

func (p *prototype) checkWarranty(c *gin.Context, barcode string) (warranty warrantyBaseDto.WarrantyMachineType, err error) {
	warranty, err = p.warrantyRepo.GetWarrantyMachineType(
		c,
		builder.NewWarranty().
			JoinMachineType("LEFT JOIN machine_type mt ON warranty.model_id = mt.model_id").
			BarcodeEq(barcode).
			StatusIn(consts.WarrantyStatusActive, consts.WarrantyStatusVirtual),
	)
	if err != nil {
		return
	}
	if warranty.Id != 0 && warranty.Status == consts.WarrantyStatusActive {
		err = errors.New("已有保卡")
		return
	}
	return warranty, nil
}

func (p *prototype) checkPrototypeEntry(c *gin.Context, barcode string, endpointId int, endpointTopAgency int) (prototypeInfo *dto.PrototypeMachineTypeConfig, err error) {
	prototypeInfo, err = p.repo.GetPrototypeMachineTypeConfig(
		c,
		prototypeBuilder.NewPrototype().
			JoinPrototypeConfigEq("LEFT JOIN prototype_config pc ON prototype.model_id = pc.model_id").
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(barcode). // 判断是否是演示样机
			StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.TopAgency > 0 && prototypeInfo.TopAgency != endpointTopAgency {
		err = errors.New("出货代理不对，无法设置为样机")
		return nil, err
	}
	if prototypeInfo.Status == consts.WarrantyStatusActive && prototypeInfo.Endpoint == endpointId {
		err = errors.New("此终端已录入过此样机")
		return nil, err
	}
	return prototypeInfo, err
}

// GetPrototypeLimit 获取代理商样机限制信息
func (p *prototype) GetPrototypeLimit(c *gin.Context, uid int) (*dto.PrototypeLimitResponse, error) {
	// 获取代理商信息
	agencyInfo, err := p.userRepo.GetAgencyInfo(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取代理商信息失败")
	}
	if agencyInfo == nil {
		return nil, errors.New("未找到代理商信息")
	}

	// 处理代理商层级逻辑
	topAgency := agencyInfo.TopAgency
	secondAgency := agencyInfo.SecondAgency
	if topAgency == nil {
		topAgency = &[]int{0}[0] // 创建指向0的指针
	}
	if secondAgency > 0 {
		topAgency = &[]int{0}[0] // 二级代理商存在时，顶级代理商设为0
	}

	// 获取样机统计
	prototypeTotal, prototypeMonthTotal, err := p.repo.GetAgencyPrototypeCount(c, *topAgency, secondAgency)
	if err != nil {
		return nil, errors.Wrap(err, "获取样机统计失败")
	}

	// 构建响应数据
	response := &dto.PrototypeLimitResponse{
		PrototypeLimit:          150, // 固定值
		PrototypeFrequencyLimit: -1,  // 固定值，-1表示无限制
		PrototypeTotal:          prototypeTotal,
		PrototypeMonthTotal:     prototypeMonthTotal,
	}

	return response, nil
}

// GetPrototypeUserList 获取代理商演示用户列表
func (p *prototype) GetPrototypeUserList(c *gin.Context, uid int) (*dto.PrototypeUserListResponse, error) {
	// 获取代理商信息
	agencyInfo, err := p.userRepo.GetAgencyInfo(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取代理商信息失败")
	}
	if agencyInfo == nil {
		return nil, errors.New("未找到代理商用户信息")
	}

	// 获取代理商层级信息
	topAgency := agencyInfo.TopAgency
	secondAgency := agencyInfo.SecondAgency
	if topAgency == nil {
		topAgency = &[]int{0}[0] // 创建指向0的指针
	}

	// 获取演示用户列表
	userList, err := p.repo.GetAgencyPrototypeUserList(c, *topAgency, secondAgency)
	if err != nil {
		return nil, errors.Wrap(err, "获取演示用户列表失败")
	}

	// 构建响应数据
	response := &dto.PrototypeUserListResponse{
		UserList:  userList,
		UserLimit: -1, // 无限制
	}

	return response, nil
}

// BindPrototypeUserToEndpoint 终端演示用户绑定
func (p *prototype) BindPrototypeUser(c *gin.Context, phone, code string, endpointID int) (*dto.PrototypeUserBind, error) {
	// 验证手机号和验证码
	if phone == "" || code == "" {
		return nil, errors.New("手机号和验证码不能为空")
	}

	// 验证手机号格式（简单验证）
	if len(phone) != 11 {
		return nil, errors.New("手机号格式不正确")
	}

	// 验证验证码
	verifyResult, err := p.userRepo.VerifyPhoneCode(c, code, phone, 5)
	if err != nil {
		return nil, errors.Wrap(err, "验证码验证失败")
	}

	switch verifyResult {
	case -1:
		return nil, errors.New("验证码不存在")
	case -2:
		return nil, errors.New("验证码不正确")
	case 2:
		return nil, errors.New("验证码已过期")
	case 1:
		// 验证成功，继续处理
	default:
		return nil, errors.New("验证码验证失败")
	}

	// 执行绑定操作
	bindResult, err := p.repo.BindPrototypeUser(c, phone, endpointID)
	if err != nil {
		return nil, errors.Wrap(err, "绑定用户失败")
	}

	return bindResult, nil
}

// ChangePrototypeUserPhone 终端演示用户修改手机号
func (p *prototype) ChangePrototypeUserPhone(c *gin.Context, endpointID int, code, oldPhone, newPhone string) error {
	// 验证新手机号格式（简单验证）
	if !utils.IsPhone(newPhone) {
		return errors.New("新手机号格式不正确")
	}

	// 验证新手机号验证码
	verifyResult, err := p.userRepo.VerifyPhoneCode(c, code, newPhone, 5)
	if err != nil {
		return errors.Wrap(err, "验证码验证失败")
	}

	switch verifyResult {
	case -1:
		return errors.New("验证码不存在")
	case -2:
		return errors.New("验证码不正确")
	case 2:
		return errors.New("验证码已过期")
	case 1:
		// 验证成功，继续处理
	default:
		return errors.New("验证码验证失败")
	}

	// 验证旧手机号是否存在且属于指定终端
	oldUser, err := p.repo.GetPrototypeUserByPhone(c, oldPhone)
	if err != nil {
		return errors.Wrap(err, "查询旧手机号用户信息失败")
	}
	if oldUser == nil {
		return errors.New("无此演示账号")
	}
	if oldUser.Endpoint == nil || *oldUser.Endpoint != endpointID {
		return errors.New("旧手机号非此终端演示账号，无法修改")
	}

	// 验证新手机号是否已被绑定
	newUser, err := p.repo.GetPrototypeUserByPhone(c, newPhone)
	if err != nil {
		return errors.Wrap(err, "查询新手机号用户信息失败")
	}
	if newUser != nil {
		return errors.New("新手机号已被绑定")
	}

	// 执行手机号修改
	err = p.repo.ChangePrototypeUserPhone(c, endpointID, oldPhone, newPhone)
	if err != nil {
		return errors.Wrap(err, "修改手机号失败")
	}

	return nil
}

// DeletePrototypeUser 终端演示用户删除
func (p *prototype) DeletePrototypeUser(c *gin.Context, endpointID int, phone string) error {
	// 验证手机号格式
	if !utils.IsPhone(phone) {
		return errors.New("手机号格式不正确")
	}

	// 验证用户是否存在且属于指定终端
	user, err := p.repo.GetPrototypeUserByPhone(c, phone)
	if err != nil {
		return errors.Wrap(err, "查询用户信息失败")
	}
	if user == nil {
		return errors.New("无此演示账号，无法删除")
	}
	if user.Endpoint == nil || *user.Endpoint != endpointID {
		return errors.New("禁止删除非此终端演示账号")
	}

	// 执行删除操作
	err = p.repo.DeletePrototypeUser(c, endpointID, phone)
	if err != nil {
		return errors.Wrap(err, "删除用户失败")
	}

	return nil
}

// PrototypeWarrantyQuery 查询样机是否有保卡或者已经入样机库
func (p *prototype) PrototypeWarrantyQuery(c *gin.Context, barcode, number, imei string) (interface{}, error) {
	// 参数验证
	if barcode == "" && number == "" {
		return nil, errors.New("参数错误")
	}

	// 调用prototype repository的PrototypeWarrantyQuery方法
	result, err := p.repo.PrototypeWarrantyQuery(c, barcode, number, imei, p.warrantyRepo)
	if err != nil {
		return nil, errors.Wrap(err, "查询样机保卡信息失败")
	}

	return result, nil
}
