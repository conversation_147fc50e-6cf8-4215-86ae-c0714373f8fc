package client

// Attachment 附件信息
type Attachment struct {
	Name string `json:"name"`
	Mime string `json:"mime"`
	Size string `json:"size"`
	URL  string `json:"url"`
}

// BugfreeDuplicateReplyRequest 重复问题回复请求
type BugfreeDuplicateReplyRequest struct {
	Uid         int          `json:"uid"`
	Id          int          `json:"id" binding:"required"` // bug 反馈ID
	Username    string       `json:"username" binding:"required"`
	PhoneNumber string       `json:"phone_number" binding:"required"`
	Content     string       `json:"content,omitempty"`     // 回复内容，可选
	Attachments []Attachment `json:"attachments,omitempty"` // 附件列表，可选
}
