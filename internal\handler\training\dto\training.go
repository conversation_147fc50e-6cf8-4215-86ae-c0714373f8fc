package dto

type TrainVideosRequest struct {
	Ts    int64 `form:"ts"`    // 时间戳
	Count int   `form:"count"` // 数量限制，默认10
}

type TrainVideoItem struct {
	ID          uint     `json:"id"`
	Title       string   `json:"title"`
	Preview     []string `json:"preview"` // 预览图数组
	Path        string   `json:"path"`    // 视频路径
	Description string   `json:"description"`
	CreatedAt   string   `json:"created_at"` // 格式化后的创建时间
	UpdatedAt   string   `json:"updated_at"` // 格式化后的更新时间
}

type TrainVideosResponse struct {
	Data  []TrainVideoItem `json:"data"`
	Size  int              `json:"size"`
	IsEnd bool             `json:"isEnd"`
}

type TrainListRequest struct {
	Page     int `form:"page"`     // 页码，默认1
	Count    int `form:"count"`    // 每页数量，默认10
	Type     int `form:"type"`     // 培训类型，-1表示所有类型
	Category int `form:"category"` // 培训分类，-1表示所有分类
	Order    int `form:"order"`    // 排序方式，1=按id倒序，2=按月下载量倒序
}

type TrainListItem struct {
	ID                 uint     `json:"id"`
	Name               string   `json:"name"`
	Preview            []string `json:"preview"`              // 预览图数组
	Path               string   `json:"path"`                 // 视频路径
	ArticleLink        *string  `json:"article_link"`         // 文章链接
	Share              uint8    `json:"share"`                // 是否可分享
	Description        string   `json:"description"`          // 描述
	CreatedAt          string   `json:"created_at"`           // 格式化后的创建时间
	UpdatedAt          string   `json:"updated_at"`           // 格式化后的更新时间
	Star               uint8    `json:"star"`                 // 是否重要
	DownloadCount      uint     `json:"download_count"`       // 下载次数
	Credit             float64  `json:"credit"`               // 学分
	CreditLearningTime uint16   `json:"credit_learning_time"` // 获得学分所需学习时长
	Collection         uint8    `json:"collection"`           // 是否收藏：1=已收藏，0=未收藏
	GetCredit          uint8    `json:"get_credit"`           // 是否已获得学分：1=已获得，0=未获得
	CollectionCount    uint     `json:"collection_count"`     // 收藏总数
	ShareURL           string   `json:"share_url"`            // 分享URL
	IsRead             uint8    `json:"is_read"`              // 是否已读：1=已读，0=未读
}

type TrainListResponse struct {
	Data  []TrainListItem `json:"data"`
	Size  int             `json:"size"`
	IsEnd bool            `json:"isEnd"`
}

type TrainTypeItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}

type TrainCategoriesRequest struct {
	Type int `form:"type"` // 培训类型，0或空表示所有类型
}

type TrainCategoryItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}

type TrainTypeCategoryItem struct {
	Type     uint   `json:"type"`
	Category uint   `json:"category"`
	Title    string `json:"title"`
}

type AddLearningHistoryRequest struct {
	ID           uint   `form:"id" binding:"required"`            // 培训ID
	LearningTime string `form:"learning_time" binding:"required"` // 学习时长
}

type AddShareHistoryRequest struct {
	TrainID uint `form:"train_id" binding:"required"` // 培训ID
}

type TrainSearchRequest struct {
	Keyword string `form:"keyword" binding:"required"` // 搜索关键词
}

type TrainSearchItem struct {
	ID                 uint     `json:"id"`
	Name               string   `json:"name"`
	Preview            []string `json:"preview"`              // 预览图数组
	Path               string   `json:"path"`                 // 视频路径
	ArticleLink        *string  `json:"article_link"`         // 文章链接
	Share              uint8    `json:"share"`                // 是否可分享
	Description        string   `json:"description"`          // 描述
	CreatedAt          string   `json:"created_at"`           // 格式化后的创建时间
	UpdatedAt          string   `json:"updated_at"`           // 格式化后的更新时间
	Star               uint8    `json:"star"`                 // 是否重要
	DownloadCount      uint     `json:"download_count"`       // 下载次数
	Credit             float64  `json:"credit"`               // 学分
	CreditLearningTime uint16   `json:"credit_learning_time"` // 获得学分所需学习时长
	GetCredit          uint8    `json:"get_credit"`           // 是否已获得学分：1=已获得，0=未获得
	ShareURL           string   `json:"share_url"`            // 分享URL
}

type TrainHotRequest struct {
	Page  int `form:"page"`  // 页码，默认1
	Count int `form:"count"` // 每页数量，默认10
}

type TrainHotItem struct {
	ID            uint     `json:"id"`
	Name          string   `json:"name"`
	Preview       []string `json:"preview"`        // 预览图数组
	Path          string   `json:"path"`           // 视频路径
	Description   string   `json:"description"`    // 描述
	ArticleLink   *string  `json:"article_link"`   // 文章链接
	Share         uint8    `json:"share"`          // 是否可分享
	CreatedAt     string   `json:"created_at"`     // 格式化后的创建时间
	UpdatedAt     string   `json:"updated_at"`     // 格式化后的更新时间
	Star          uint8    `json:"star"`           // 是否重要
	DownloadCount uint     `json:"download_count"` // 下载次数
	ShareURL      string   `json:"share_url"`      // 分享URL
}

type TrainHotResponse struct {
	Data  []TrainHotItem `json:"data"`
	Size  int            `json:"size"`
	IsEnd bool           `json:"isEnd"`
}

type AddCollectionRequest struct {
	TrainID uint `form:"train_id" binding:"required"` // 培训ID
}

type CancelCollectionRequest struct {
	TrainID uint `form:"train_id" binding:"required"` // 培训ID
}

type CollectionListRequest struct {
	Page  int `form:"page"`  // 页码，默认1
	Count int `form:"count"` // 每页数量，默认10
}

type CollectionListItem struct {
	ID              uint     `json:"id"`
	Name            string   `json:"name"`
	Preview         []string `json:"preview"`          // 预览图数组
	Path            string   `json:"path"`             // 视频路径
	ArticleLink     *string  `json:"article_link"`     // 文章链接
	Share           uint8    `json:"share"`            // 是否可分享
	Description     string   `json:"description"`      // 描述
	CreatedAt       string   `json:"created_at"`       // 格式化后的创建时间
	UpdatedAt       string   `json:"updated_at"`       // 格式化后的更新时间
	Star            uint8    `json:"star"`             // 是否重要
	DownloadCount   uint     `json:"download_count"`   // 下载次数
	Collection      uint8    `json:"collection"`       // 收藏状态：1=已收藏，0=未收藏
	CollectionCount uint     `json:"collection_count"` // 收藏总数
	ShareURL        string   `json:"share_url"`        // 分享URL
	IsRead          uint8    `json:"is_read"`          // 是否已读：1=已读，0=未读
}

type CollectionListResponse struct {
	Data  []CollectionListItem `json:"data"`
	Size  int                  `json:"size"`
	IsEnd bool                 `json:"isEnd"`
}

type TrainBannerRequest struct {
	Type int `form:"type" binding:"required"` // 培训类型，不能为空
}

type TrainBannerItem struct {
	ID            uint     `json:"id"`
	Name          string   `json:"name"`
	Preview       []string `json:"preview"`        // 预览图数组
	Path          string   `json:"path"`           // 视频路径
	ArticleLink   *string  `json:"article_link"`   // 文章链接
	Share         uint8    `json:"share"`          // 是否可分享
	Description   string   `json:"description"`    // 描述
	CreatedAt     string   `json:"created_at"`     // 格式化后的创建时间
	UpdatedAt     string   `json:"updated_at"`     // 格式化后的更新时间
	Star          uint8    `json:"star"`           // 是否重要
	DownloadCount uint     `json:"download_count"` // 下载次数
	BannerImage   string   `json:"banner_image"`   // Banner图片
	Collection    uint8    `json:"collection"`     // 收藏状态：1=已收藏，0=未收藏
	ShareURL      string   `json:"share_url"`      // 分享URL
}
