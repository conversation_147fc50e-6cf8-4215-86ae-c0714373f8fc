package dto

// TeamInfoRequest 团队信息请求
type TeamInfoRequest struct {
	// 暂时不需要额外参数，通过token认证获取用户信息
}

// TeamMember 团队成员信息
type TeamMember struct {
	ID            int     `json:"id"`
	Username      string  `json:"username"`
	Avatar        *string `json:"avatar,omitempty"`
	Name          *string `json:"name,omitempty"`
	ExtendName    *string `json:"extend_name,omitempty"`
	ExtendPhone   *string `json:"extend_phone,omitempty"`
	WechatID      *string `json:"wechat_id,omitempty"`
	WechatImage   *string `json:"wechat_image,omitempty"`
	QrCodeAddress *string `json:"qr_code_address,omitempty"`
	Role          string  `json:"role"`
	Status        int8    `json:"status"`
}

// TeamInfoResponse 团队信息响应
type TeamInfoResponse struct {
	Data TeamData `json:"data"`
}

// TeamData 团队数据结构
type TeamData struct {
	Manager    []TeamMember `json:"manager"`    // 店长和代理店长列表
	Salesclerk []TeamMember `json:"salesclerk"` // 店员列表
}
