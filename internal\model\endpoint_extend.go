package model

import (
	"time"
)

// EndpointExtend 终端拓展信息模型
type EndpointExtend struct {
	ID                int       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Endpoint          int       `gorm:"not null;column:endpoint" json:"endpoint" comment:"终端id"`
	ChannelLevel      *int8     `gorm:"type:tinyint(1);column:channel_level" json:"channel_level,omitempty" comment:"渠道级别"`
	ChannelType       *int8     `gorm:"type:tinyint(1);column:channel_type" json:"channel_type,omitempty" comment:"渠道类别"`
	PositionPriority  *string   `gorm:"type:varchar(100);column:position_priority" json:"position_priority,omitempty" comment:"终端位置"`
	Area              *int      `gorm:"type:int(6);column:area" json:"area,omitempty" comment:"终端面积"`
	Sales             *int      `gorm:"type:int(10);column:sales" json:"sales,omitempty" comment:"销售额,单位为万"`
	CreatedAt         time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	UpdatedAt         time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"`
	Storefront        string    `gorm:"type:text;not null;column:storefront" json:"storefront" comment:"门头图，json数组"`
	ProductImage      string    `gorm:"type:text;not null;column:product_image" json:"product_image" comment:"产品形象区，json数组"`
	ProductExperience string    `gorm:"type:text;not null;column:product_experience" json:"product_experience" comment:"产品体验区，json数组"`
	CultureWall       string    `gorm:"type:text;not null;column:culture_wall" json:"culture_wall" comment:"文化墙，json数组"`
	Cashier           string    `gorm:"type:text;not null;column:cashier" json:"cashier" comment:"收银区，json数组"`
	RestArea          string    `gorm:"type:text;not null;column:rest_area" json:"rest_area" comment:"休息区，json数组"`
	SpareParts        string    `gorm:"type:text;not null;column:spare_parts" json:"spare_parts" comment:"礼品配件区，json数组"`
	BooksBorrow       string    `gorm:"type:text;not null;column:books_borrow" json:"books_borrow" comment:"图书借阅区，json数组"`
	TrainingRoom      string    `gorm:"type:text;not null;column:training_room" json:"training_room" comment:"培训室，json数组"`
	Other             string    `gorm:"type:text;not null;column:other" json:"other" comment:"其它区域，json数组"`
}

// TableName 指定表名
func (EndpointExtend) TableName() string {
	return "endpoint_extend"
}
