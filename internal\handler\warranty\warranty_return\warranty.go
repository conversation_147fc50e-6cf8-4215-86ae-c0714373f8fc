package warranty_return

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/warranty/warranty_return/dto"
	"marketing-app/internal/pkg/convertor/warranty_convertor"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/router/warranty/warranty_return/client"
	service "marketing-app/internal/service/warranty/warranty_return"
	"time"
)

type WarrantyReturnHandler interface {
	ReturnWarranty(c *gin.Context)
}

type Warranty struct {
	warrantySvc service.WarrantyReturnService
}

func NewWarrantyHandler(svc service.WarrantyReturnService) WarrantyReturnHandler {
	return &Warranty{
		warrantySvc: svc,
	}
}

func (w *Warranty) ReturnWarranty(c *gin.Context) {
	var (
		req  client.ReturnWarrantyRequest
		err  error
		resp dto.ReturnWarrantyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	req.Uid = int(uid)

	if err = utils.DateFormatValidate(time.DateOnly, req.ReturnDate); err != nil {
		err = errors.Wrap(err, "date format error")
		return
	}
	err = w.warrantySvc.ReturnWarranty(
		c,
		warranty_convertor.NewWarrantyReturnConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = dto.ReturnWarrantyResp{
		Message: fmt.Sprint("退机成功！"),
	}
}
