package repository

import (
	"encoding/json"
	"fmt"
	"marketing-app/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// NotificationMessage 查询结果结构
type NotificationMessage struct {
	ID        uint            `json:"id"`
	CreatedAt time.Time       `json:"created_at"`
	Content   json.RawMessage `json:"content"`
	Slug      string          `json:"slug"`
	Read      int8            `json:"read"`
	Checked   int8            `json:"checked"`
}

type NotificationRepository interface {
	// GetNotificationTypes 获取消息类型(只算近60天有消息的类型，计算未读数)
	GetNotificationTypes(c *gin.Context, userID uint) ([]map[string]any, error)
	// GetUserNotifications 获取用户通知消息
	GetUserNotifications(c *gin.Context, userID uint, platform string, pageSize, page int) ([]NotificationMessage, error)
	// GetUserNotificationDetail 获取用户单条通知详情
	GetUserNotificationDetail(c *gin.Context, userID uint, messageID uint) (*NotificationMessage, error)
	// UpdateInboxFetched 更新收件箱拉取状态
	UpdateInboxFetched(c *gin.Context, ids []uint) error
	// UpdateNotificationStatus 更新通知状态
	UpdateNotificationStatus(c *gin.Context, userID uint, ids []uint, column string) error
}

type notificationRepository struct {
	db *gorm.DB
}

func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{
		db: db,
	}
}

func (r *notificationRepository) GetUserNotifications(c *gin.Context, userID uint, platform string, pageSize, page int) ([]NotificationMessage, error) {
	var messages []NotificationMessage

	query := r.db.WithContext(c).
		Table("app_notification_inbox as i").
		Select("i.created_at, n.content, i.id, t.slug, i.read, i.checked").
		Joins("JOIN app_notification as n ON i.notification_id = n.id").
		Joins("JOIN app_notification_type as t ON n.type_id = t.id").
		Where("i.user_id = ?", userID)
	if platform != "" {
		query = query.Where("n.platform LIKE ?", fmt.Sprintf("%%%s%%", platform))
	}

	offset := (page - 1) * pageSize
	query = query.Where("n.revoked = ?", 0).
		Order("i.id").
		Limit(pageSize).Offset(offset)

	//查询两个月内的通知
	twoMonthsAgo := time.Now().AddDate(0, -2, 0).Format("2006-01-02 15:04:05")
	query = query.Where("i.created_at >= ?", twoMonthsAgo)

	err := query.Find(&messages).Error
	return messages, err
}

func (r *notificationRepository) GetUserNotificationDetail(c *gin.Context, userID uint, messageID uint) (*NotificationMessage, error) {
	var message NotificationMessage

	query := r.db.WithContext(c).
		Table("app_notification_inbox as i").
		Select("i.created_at, n.content, i.id, t.slug, i.read, i.checked").
		Joins("JOIN app_notification as n ON i.notification_id = n.id").
		Joins("JOIN app_notification_type as t ON n.type_id = t.id").
		Where("i.user_id = ? AND i.id = ?", userID, messageID).
		Where("n.revoked = ?", 0)

	err := query.First(&message).Error
	if err != nil {
		return nil, err
	}

	return &message, nil
}

func (r *notificationRepository) UpdateInboxFetched(c *gin.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	return r.db.WithContext(c).
		Model(&model.AppNotificationInbox{}).
		Where("id IN ?", ids).
		Update("fetched", 1).Error
}

func (r *notificationRepository) UpdateNotificationStatus(c *gin.Context, userID uint, ids []uint, column string) error {
	if len(ids) == 0 {
		return nil
	}

	// 验证column参数，只允许更新read和checked字段
	if column != "read" && column != "checked" {
		return fmt.Errorf("invalid column: %s", column)
	}

	return r.db.WithContext(c).
		Model(&model.AppNotificationInbox{}).
		Where("user_id = ? AND id IN ?", userID, ids).
		Update(column, 1).Error
}
