package endpoint

import (
	"encoding/json"
	"errors"
	"strings"
)

const (
	// IMG_HOST 图片服务器地址，对应Python版本的IMG_HOST
	IMG_HOST = "https://dt.readboy.com/"
)

// loadImages 将JSON字符串转换为完整的图片URL数组
// 对应Python版本的_loadImages函数
func loadImages(imagesJSON *string) []string {
	var result []string

	if imagesJSON == nil || *imagesJSON == "" {
		return result
	}

	var imageFiles []string
	err := json.Unmarshal([]byte(*imagesJSON), &imageFiles)
	if err != nil {
		// 如果JSON解析失败，返回空数组
		return result
	}

	// 为每个图片文件添加完整URL前缀
	for _, img := range imageFiles {
		if img != "" {
			// 确保图片路径不以/开头，避免重复的斜杠
			imgPath := strings.TrimPrefix(img, "/")
			result = append(result, IMG_HOST+imgPath)
		}
	}

	return result
}

// dumpImages 将图片URL数组转换为JSON字符串（去除域名前缀）
// 对应Python版本的_dumpImages函数
func dumpImages(imageURLs []string) (string, error) {
	var imageFiles []string

	for _, url := range imageURLs {
		if url != "" {
			// 检查URL是否以IMG_HOST开头
			if strings.HasPrefix(url, IMG_HOST) {
				// 去除IMG_HOST前缀，保留相对路径
				imagePath := strings.TrimPrefix(url, IMG_HOST)
				imageFiles = append(imageFiles, imagePath)
			} else {
				// 如果URL不以IMG_HOST开头，抛出错误（模拟Python版本的异常）
				return "", errors.New("invalid image url=" + url)
			}
		}
	}

	// 将数组转换为JSON字符串
	jsonBytes, err := json.Marshal(imageFiles)
	if err != nil {
		return "", err
	}

	return string(jsonBytes), nil
}
