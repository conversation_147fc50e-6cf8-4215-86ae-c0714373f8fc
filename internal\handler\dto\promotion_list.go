package dto

import "time"

// PromotionListsReq 促销活动列表请求
type PromotionListsReq struct {
	Id       int    `form:"id" json:"id" binding:"required"` // 促销活动ID
	Agency   int    `form:"agency" json:"agency"`            // 代理商ID
	Endpoint int    `form:"endpoint" json:"endpoint"`        // 终端ID
	Receipt  int    `form:"receipt" json:"receipt"`          // 回执状态 1-已上传 2-未上传
	ModelId  []int  `form:"model_id" json:"model_id"`        // 机型ID列表
	Keyword  string `form:"keyword" json:"keyword"`          // 关键词搜索(条码)
	Page     int    `form:"page" json:"page"`                // 页码
	PageSize int    `form:"page_size" json:"page_size"`      // 每页数量
}

// PromotionListsResp 促销活动列表响应
type PromotionListsResp struct {
	List     []PromotionListItem `json:"list"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
}

// PromotionListItem 促销活动列表项
type PromotionListItem struct {
	Id               int        `json:"id"`
	SalesPromotionId int        `json:"sales_promotion_id"`
	Endpoint         int        `json:"endpoint"`
	ModelId          int        `json:"model_id"`
	Barcode          string     `json:"barcode"`
	BuyDate          *time.Time `json:"buy_date"`
	WarrantyId       int        `json:"warranty_id"`
	IsReceipt        int        `json:"is_receipt"`
	ReceiptAt        *time.Time `json:"receipt_at"`
	RegionName       string     `json:"region_name"`
	AgencyName       string     `json:"agency_name"`
	EndpointName     string     `json:"endpoint_name"`
	EndpointCode     string     `json:"endpoint_code"`
	Manager          string     `json:"manager"`
	EndpointPhone    string     `json:"endpoint_phone"`
	Address          string     `json:"address"`
	Receipt          string     `json:"receipt"` // 可能是string或[]PromotionListReceiptResp
	Number           string     `json:"number,omitempty"`
	ActivatedAt      string     `json:"activated_at,omitempty"`
	StudentName      string     `json:"student_name,omitempty"`
}

// PromotionListDetailReq 促销活动详情请求
type PromotionListDetailReq struct {
	Id       int `uri:"id" binding:"required,min=1"` // 促销活动列表ID
	Endpoint int
}

// PromotionListDetailResp 促销活动详情响应
type PromotionListDetailResp struct {
	Id               int        `json:"id"`
	SalesPromotionId int        `json:"sales_promotion_id"`
	Endpoint         int        `json:"endpoint"`
	ModelId          int        `json:"model_id"`
	Barcode          string     `json:"barcode"`
	BuyDate          *time.Time `json:"buy_date"`
	WarrantyId       int        `json:"warranty_id"`
	IsReceipt        int        `json:"is_receipt"`
	ReceiptAt        *time.Time `json:"receipt_at"`
	StudentUid       string     `json:"student_uid"`
	StudentName      string     `json:"student_name"`
	AgencyName       string     `json:"agency_name"`
	ActivatedAt      string     `json:"activated_at"`
	Number           string     `json:"number"`
	HourInterval     int        `json:"hour_interval"`
	EndpointCode     string     `json:"endpoint_code"`
	EndpointName     string     `json:"endpoint_name"`
	Manager          string     `json:"manager"`
	ReturnAt         *time.Time `json:"return_at"`
	Receipt          string     `json:"receipt"` // 可能是string或[]PromotionListReceiptResp
}

// ReceiptUploadReq 上传回执请求
type ReceiptUploadReq struct {
	Id      int `uri:"id" binding:"required,min=1"` // 促销活动列表ID
	Receipt string
}

// ReceiptUploadResp 上传回执响应
type ReceiptUploadResp struct {
	Message string `json:"message"`
}

// PromotionListReceiptResp 回执响应
type PromotionListReceiptResp struct {
	SalesPromotionListId int    `json:"sales_promotion_list_id"`
	Receipt              string `json:"receipt"`
	Number               string `json:"number"`
	Count                int    `json:"count"`
}

// PromotionsReq 促销活动列表请求
type PromotionsReq struct {
	Type     int `form:"type" json:"type"`           // 活动类型
	Agency   int `form:"agency" json:"agency"`       // 代理商ID
	Endpoint int `form:"endpoint" json:"endpoint"`   // 终端ID
	Page     int `form:"page" json:"page"`           // 页码
	PageSize int `form:"page_size" json:"page_size"` // 每页数量
}

// PromotionsResp 促销活动列表响应
type PromotionsResp struct {
	List     []PromotionItem `json:"list"`
	Total    int64           `json:"total"`
	Page     int             `json:"page"`
	PageSize int             `json:"page_size"`
}

// PromotionItem 促销活动项
type PromotionItem struct {
	Id           int        `json:"id"`
	Name         string     `json:"name"`
	Type         int        `json:"type"`
	ListCount    int        `json:"list_count"`    // 活动人数
	ReceiptCount int        `json:"receipt_count"` // 回执人数
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
}

// PromotionsCount 促销活动统计
type PromotionsCount struct {
	Id           int `json:"id"`
	ListCount    int `json:"list_count"`
	ReceiptCount int `json:"receipt_count"`
}
