package model

import (
	"time"
)

type TrainVideos struct {
	ID          uint      `gorm:"primaryKey;autoIncrement;comment:唯一标识" json:"id"`
	Title       string    `gorm:"type:varchar(50);not null;default:'';comment:标题" json:"title"`
	Preview     string    `gorm:"type:text;not null;comment:预览图" json:"preview"`
	Path        string    `gorm:"type:text;not null;comment:视频路径" json:"path"`
	Description string    `gorm:"type:text;not null;comment:描述" json:"description"`
	Status      uint8     `gorm:"type:smallint(3) unsigned;not null;default:0;comment:状态0为禁用，1为可用" json:"status"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TrainVideos) TableName() string {
	return "train_videos"
}
