package dto

type EndpointAgency struct {
	Id            int
	TopAgency     int
	SecondAgency  int
	IsDirectSales int
	Compound      int
	Status        int

	Channel string `gorm:"column:endpoint_channel"`
}

// EndpointItem 终端列表项
type EndpointItem struct {
	ID          int     `json:"id"`                 // 终端ID
	Name        string  `json:"name"`               // 终端名称
	Phone       *string `json:"phone"`              // 电话
	Address     string  `json:"address"`            // 地址
	City        string  `json:"city"`               // 城市
	Lng         float64 `json:"lng"`                // 经度
	Lat         float64 `json:"lat"`                // 纬度
	Blng        float64 `json:"blng"`               // 百度经度
	Blat        float64 `json:"blat"`               // 百度纬度
	IsPreSale   int     `json:"is_pre_sale"`        // 是否售前：1-是，0-否
	IsAfterSale int     `json:"is_after_sale"`      // 是否售后：1-是，0-否
	Distance    float64 `json:"distance,omitempty"` // 距离（仅按位置查询时返回）
}

// EndpointsResponse 终端列表响应
type EndpointsResponse struct {
	Data []EndpointItem `json:"data"` // 终端列表
}

// EndpointInfoByUsername 根据用户名获取终端信息（Repository层DTO）
type EndpointInfoByUsername struct {
	ID           int     `json:"id" gorm:"column:id"`
	Name         string  `json:"name" gorm:"column:name"`
	TopAgency    *string `json:"top_agency" gorm:"column:top_agency"`
	SecondAgency *string `json:"second_agency" gorm:"column:second_agency"`
	Address      string  `json:"address" gorm:"column:address"`
	Manager      *string `json:"manager" gorm:"column:manager"`
	Phone        *string `json:"phone" gorm:"column:phone"`
}

// ChannelType 渠道类型（Repository层DTO）
type ChannelType struct {
	ID   int    `json:"id" gorm:"column:id"`
	Name string `json:"name" gorm:"column:name"`
}

// EndpointImageInfo 终端形象信息（Repository层DTO）
type EndpointImageInfo struct {
	Name             string  `json:"name" gorm:"column:name"`
	Manager          *string `json:"manager" gorm:"column:manager"`
	Phone            *string `json:"phone" gorm:"column:phone"`
	Images           *string `json:"images" gorm:"column:images"`
	Lng              string  `json:"lng" gorm:"column:lng"`
	Lat              string  `json:"lat" gorm:"column:lat"`
	Code             string  `json:"code" gorm:"column:code"`
	Endpoint         *int    `json:"endpoint" gorm:"column:endpoint"`
	ChannelLevel     *int8   `json:"channel_level" gorm:"column:channel_level"`
	ChannelType      *int8   `json:"channel_type" gorm:"column:channel_type"`
	PositionPriority *string `json:"position_priority" gorm:"column:position_priority"`
	Area             *int    `json:"area" gorm:"column:area"`
	Sales            *int    `json:"sales" gorm:"column:sales"`
	CreatedAt        string  `json:"created_at" gorm:"column:created_at"`
}

// CompetitorInfo 竞争品牌信息（Repository层DTO）
type CompetitorInfo struct {
	ID               int     `json:"id" gorm:"column:id"`
	Brand            string  `json:"brand" gorm:"column:brand"`
	PositionPriority *string `json:"position_priority" gorm:"column:position_priority"`
	Pics             *string `json:"pics" gorm:"column:pics"`
}

// EndpointNewImageInfo 终端形象变更信息（Repository层DTO）
type EndpointNewImageInfo struct {
	ID                 int     `json:"id" gorm:"column:id"`
	Name               string  `json:"name" gorm:"column:name"`
	Manager            *string `json:"manager" gorm:"column:manager"`
	Phone              *string `json:"phone" gorm:"column:phone"`
	Endpoint           int     `json:"endpoint" gorm:"column:endpoint"`
	PositionPriority   *string `json:"position_priority" gorm:"column:position_priority"`
	Area               *int    `json:"area" gorm:"column:area"`
	ChannelLevel       *int8   `json:"channel_level" gorm:"column:channel_level"`
	ChannelType        *int8   `json:"channel_type" gorm:"column:channel_type"`
	Sales              *int    `json:"sales" gorm:"column:sales"`
	Images             *string `json:"images" gorm:"column:images"`
	Attachment         *string `json:"attachment" gorm:"column:attachment"`
	Remark             *string `json:"remark" gorm:"column:remark"`
	TopAgencyOpinion   *string `json:"top_agency_opinion" gorm:"column:top_agency_opinion"`
	Lat                string  `json:"lat" gorm:"column:lat"`
	Lng                string  `json:"lng" gorm:"column:lng"`
	Status             int8    `json:"status" gorm:"column:status"`
	IsAuditAgain       int8    `json:"is_audit_again" gorm:"column:is_audit_again"`
	TopAgencyAuditedAt *string `json:"top_agency_audited_at" gorm:"column:top_agency_audited_at"`
	ManagerAuditedAt   *string `json:"manager_audited_at" gorm:"column:manager_audited_at"`
	CreatedAt          *string `json:"created_at" gorm:"column:created_at"`
	UpdatedAt          *string `json:"updated_at" gorm:"column:updated_at"`
	ManagerOpinion     *string `json:"manager_opinion" gorm:"column:manager_opinion"`
}

// AuditCompetitorInfo 审核竞争品牌信息（Repository层DTO）
type AuditCompetitorInfo struct {
	Brand            string  `json:"brand" gorm:"column:brand"`
	PositionPriority *string `json:"position_priority" gorm:"column:position_priority"`
	Pics             *string `json:"pics" gorm:"column:pics"`
}

// NoticeInfo 终端公告信息（Repository层DTO）
type NoticeInfo struct {
	ID        int    `json:"id" gorm:"column:id"`
	Content   string `json:"content" gorm:"column:content"`
	Title     string `json:"title" gorm:"column:title"`
	Author    string `json:"author" gorm:"column:author"`
	CreatedAt string `json:"created_at" gorm:"column:created_at"`
}

// NoticeListResponse 终端公告列表响应（Repository层DTO）
type NoticeListResponse struct {
	Data  []NoticeInfo `json:"data"`
	Size  int          `json:"size"`
	IsEnd bool         `json:"isEnd"`
}

// EndpointStatItem 终端统计项（Repository层DTO）
type EndpointStatItem struct {
	Type  int8 `json:"type" gorm:"column:type"`   // 终端类型
	Total int  `json:"total" gorm:"column:total"` // 数量统计
}

// EndpointDetailItem 终端详情项（Repository层DTO）
type EndpointDetailItem struct {
	Name         string `json:"name" gorm:"column:name"`                   // 终端名称
	Type         int8   `json:"type" gorm:"column:type"`                   // 终端类型
	TopAgency    int    `json:"top_agency" gorm:"column:top_agency"`       // 一级代理
	SecondAgency int    `json:"second_agency" gorm:"column:second_agency"` // 二级代理
}

// EndpointDetailResponse 终端详情响应（Repository层DTO）
type EndpointDetailResponse struct {
	Detail []EndpointDetailItem `json:"detail"` // 详情列表
	IsEnd  bool                 `json:"isEnd"`  // 是否最后一页
}

// RegionItem 区域信息项（Repository层DTO）
type RegionItem struct {
	ID    int    `json:"id" gorm:"column:id"`       // 区域ID
	PID   int    `json:"pid" gorm:"column:pid"`     // 父级区域ID
	Name  string `json:"name" gorm:"column:name"`   // 区域名称
	Level int    `json:"level" gorm:"column:level"` // 区域级别
}
