package model

import (
	"time"
)

// EndpointAuditCompeteBrand 终端审核竞争对手品牌模型
type EndpointAuditCompeteBrand struct {
	ID               int        `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	AuditID          int        `gorm:"not null;column:audit_id" json:"audit_id" comment:"终端审核表的id"`
	Brand            string     `gorm:"type:varchar(255);not null;column:brand" json:"brand" comment:"竞争对手的品牌"`
	PositionPriority *string    `gorm:"type:varchar(50);column:position_priority" json:"position_priority,omitempty" comment:"所占位置(比如商场的位置,1号位,2号位这些)"`
	Pics             *string    `gorm:"type:text;column:pics" json:"pics,omitempty" comment:"竞争品牌的终端图片"`
	CreatedAt        time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	UpdatedAt        *time.Time `gorm:"column:updated_at" json:"updated_at,omitempty"`
}

// TableName 指定表名
func (EndpointAuditCompeteBrand) TableName() string {
	return "endpoint_audit_compete_brand"
}
