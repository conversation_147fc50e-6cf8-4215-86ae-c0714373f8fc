package team

import (
	handlerDTO "marketing-app/internal/handler/team/dto"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/repository/endpoint/user_endpoint"
	"marketing-app/internal/repository/team"
	repoDTO "marketing-app/internal/repository/team/dto"
	"marketing-app/internal/service/team/entity"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TeamSvc interface {
	GetTeamInfo(c *gin.Context, param *entity.TeamInfoEntity) (*handlerDTO.TeamInfoResponse, error)
}

type teamSvc struct {
	teamRepo         team.TeamRepo
	userEndpointRepo user_endpoint.UserEndpoint
}

func NewTeamSvc(teamRepo team.TeamRepo, userEndpointRepo user_endpoint.UserEndpoint) TeamSvc {
	return &teamSvc{
		teamRepo:         teamRepo,
		userEndpointRepo: userEndpointRepo,
	}
}

// GetTeamInfo 获取团队信息
func (s *teamSvc) GetTeamInfo(c *gin.Context, param *entity.TeamInfoEntity) (*handlerDTO.TeamInfoResponse, error) {
	// 1. 获取用户关联的终端信息
	endpoint, err := s.userEndpointRepo.GetEndpointAgencyByUid(c, int(param.UID))
	if err != nil {
		return nil, errors.Wrap(err, "获取用户终端信息失败")
	}

	if endpoint == nil || endpoint.ID == 0 {
		return nil, errors.New("权限不足")
	}

	// 2. 获取店长/代理店长信息
	managerList, err := s.teamRepo.GetManagerList(c, endpoint.ID)
	if err != nil {
		return nil, errors.Wrap(err, "获取店长信息失败")
	}

	// 3. 获取店员信息
	salesclerkList, err := s.teamRepo.GetSalesclerkList(c, endpoint.ID)
	if err != nil {
		return nil, errors.Wrap(err, "获取店员信息失败")
	}

	// 4. 转换为handler DTO并处理头像URL
	managerHandlerList := s.convertToHandlerDTO(managerList)
	salesclerkHandlerList := s.convertToHandlerDTO(salesclerkList)

	// 5. 处理头像URL
	s.processAvatarURLs(managerHandlerList)
	s.processAvatarURLs(salesclerkHandlerList)

	// 6. 构建响应格式
	result := &handlerDTO.TeamInfoResponse{
		Data: handlerDTO.TeamData{
			Manager:    managerHandlerList,
			Salesclerk: salesclerkHandlerList,
		},
	}

	return result, nil
}

// convertToHandlerDTO 将repository DTO转换为handler DTO
func (s *teamSvc) convertToHandlerDTO(repoList []repoDTO.TeamMember) []handlerDTO.TeamMember {
	handlerList := make([]handlerDTO.TeamMember, 0, len(repoList))

	for _, repo := range repoList {
		handler := handlerDTO.TeamMember{
			ID:            int(repo.ID),
			Username:      repo.Username,
			Avatar:        repo.Avatar,
			Name:          repo.Name,
			ExtendName:    repo.ExtendName,
			ExtendPhone:   repo.ExtendPhone,
			WechatID:      repo.WechatID,
			WechatImage:   repo.WechatImage,
			QrCodeAddress: repo.QrCodeAddress,
			Role:          repo.Role,
			Status:        repo.Status,
		}
		handlerList = append(handlerList, handler)
	}

	return handlerList
}

// processAvatarURLs 处理头像URL，添加OSS域名前缀
func (s *teamSvc) processAvatarURLs(members []handlerDTO.TeamMember) {
	ossConfig, _ := config.GetOSSConfig()

	for i := range members {
		if members[i].Avatar != nil && *members[i].Avatar != "" {
			avatarURL := ossConfig.CName + *members[i].Avatar
			members[i].Avatar = &avatarURL
		}
	}
}
