package training

import (
	"marketing-app/internal/handler/training"
	"marketing-app/internal/pkg/db"
	trainingRepo "marketing-app/internal/repository/training"
	trainingSvc "marketing-app/internal/service/training"

	"github.com/gin-gonic/gin"
)

type TrainingRouter interface {
	Register(r *gin.RouterGroup)
}

type trainingRouter struct{}

func NewTrainingRouter() TrainingRouter {
	return &trainingRouter{}
}

func (tr *trainingRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	database, _ := db.GetDB()

	// 初始化各层组件
	trainingRepository := trainingRepo.NewTraining(database)
	trainingService := trainingSvc.NewTrainingService(trainingRepository)
	trainingHandler := training.NewTrainingHandler(trainingService)

	// 注册路由
	r.GET("/videos", trainingHandler.GetTrainVideos)
	r.GET("/list", trainingHandler.GetTrainList)
	r.GET("/types", trainingHandler.GetTrainTypes)
	r.GET("/categories", trainingHandler.GetTrainCategories)
	r.GET("/type-categories", trainingHandler.GetTrainTypeCategories)
	r.POST("/learning-history", trainingHandler.AddLearningHistory)
	r.POST("/share-history", trainingHandler.AddShareHistory)
	r.GET("/search", trainingHandler.SearchTrains)
	r.GET("/hot", trainingHandler.GetTrainHot)
	r.POST("/collection", trainingHandler.AddCollection)
	r.DELETE("/collection", trainingHandler.CancelCollection)
	r.GET("/collections", trainingHandler.GetCollectionList)
	r.GET("/banner", trainingHandler.GetTrainBanner)
}
