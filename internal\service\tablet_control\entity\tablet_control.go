package entity

// AllowUnknownAppRequest AllowUnknownApp函数的POST请求实体
type AllowUnknownAppRequest struct {
	Barcode    string // 条码（可选，三选一）
	Number     string // 序列号（可选，三选一）
	IMEI       string // IMEI码（可选，三选一）
	EndpointID int    // 终端ID（必填）
	Status     int    // 状态：0=禁止，1=允许
	UID        uint   // 用户ID，从上下文获取
}

// VerifyPhoneCodeForAllowUnknownAppRequest AllowUnknownApp函数的GET请求实体（验证手机验证码）
type VerifyPhoneCodeForAllowUnknownAppRequest struct {
	Phone string // 手机号
	Code  string // 验证码
}

// GetAllowNsfwUrlRequest GetAllowNsfwUrl函数的GET请求实体
type GetAllowNsfwUrlRequest struct {
	Barcode string // 条码（可选，三选一）
	Number  string // 序列号（可选，三选一）
	IMEI    string // IMEI码（可选，三选一）
}

// SetAllowNsfwUrlRequest SetAllowNsfwUrl函数的POST请求实体
type SetAllowNsfwUrlRequest struct {
	Barcode    string // 条码（可选，三选一）
	Number     string // 序列号（可选，三选一）
	IMEI       string // IMEI码（可选，三选一）
	EndpointID int    // 终端ID（必填）
	Status     int    // 状态值（必填）
}

// CancelBindingsRequest CancelBindings函数的请求实体
type CancelBindingsRequest struct {
	Barcode string // 条码（可选，三选一）
	Number  string // 序列号（可选，三选一）
	IMEI    string // IMEI码（可选，三选一）
}

// UnbindWatchRequest UnbindWatch函数的请求实体
type UnbindWatchRequest struct {
	Barcode  string // 条码（可选，三选一）
	Number   string // 序列号（可选，三选一）
	IMEI     string // IMEI码（可选，三选一）
	Username string // 用户名（可选）
}
