package team

import (
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/team/dto"
	"marketing-app/internal/pkg/convertor/team_convertor"
	"marketing-app/internal/service/team"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Team interface {
	GetTeamInfo(c *gin.Context)
}

type teamHandler struct {
	teamSvc team.TeamSvc
}

func NewTeamHandler(svc team.TeamSvc) Team {
	return &teamHandler{teamSvc: svc}
}

// GetTeamInfo 获取团队信息
func (t *teamHandler) GetTeamInfo(c *gin.Context) {
	var (
		err  error
		resp *dto.TeamInfoResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 调用service层获取团队信息
	resp, err = t.teamSvc.GetTeamInfo(
		c,
		team_convertor.NewTeamInfoConvertor().ToEntity(uid),
	)
	if err != nil {
		return
	}

	if resp == nil {
		err = errors.New("未找到数据")
		return
	}
}
