package model

import (
	"time"

	"gorm.io/gorm"
)

// Agency 代理模型
type Agency struct {
	ID                uint           `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Name              string         `gorm:"type:varchar(128);not null;column:name" json:"name" comment:"代理名称"`
	PID               int            `gorm:"not null;default:0;column:pid" json:"pid" comment:"父id"`
	Level             int            `gorm:"not null;column:level" json:"level" comment:"代理等级,目前只有一级二级"`
	Partition         int            `gorm:"not null;default:0;column:partition" json:"partition" comment:"总代所属大区id，二级代理没有大区"`
	Department        uint8          `gorm:"type:tinyint(3) unsigned;not null;default:0;column:department" json:"department" comment:"部门归属，1渠道部华南大区，2渠道部华东大区，3渠道部华北大区，4渠道部西南大区，0未知"`
	Order             int            `gorm:"not null;default:0;column:order" json:"order" comment:"排序"`
	LetterName        *string        `gorm:"type:varchar(50);column:letter_name" json:"letter_name,omitempty" comment:"代理的缩写,是代理名称的文字拼音首字母"`
	MarketCoefficient string         `gorm:"type:varchar(50);default:0;column:market_coefficient" json:"market_coefficient" comment:"市场系数"`
	PupilsNum         uint           `gorm:"type:int(10) unsigned;default:0;column:pupils_num" json:"pupils_num" comment:"小学生数量"`
	JuniorsNum        uint           `gorm:"type:int(10) unsigned;default:0;column:juniors_num" json:"juniors_num" comment:"初中生数量"`
	StudentsNum       uint           `gorm:"type:int(11) unsigned;default:0;column:students_num" json:"students_num" comment:"在校学生数"`
	StudentsPercent   *string        `gorm:"type:varchar(20);column:students_percent" json:"students_percent,omitempty" comment:"全国在校学生数占比"`
	MarketType        string         `gorm:"type:enum('','A','B','C','D');default:'';column:market_type" json:"market_type" comment:"A类，在校学生数占比>=2%, B类 >=0.5%且<2%，C类<0.5"`
	IsFlat            uint8          `gorm:"type:tinyint(3) unsigned;not null;default:0;column:is_flat" json:"is_flat" comment:"是否扁平化"`
	FlatCity          uint           `gorm:"type:int(10) unsigned;not null;default:0;column:flat_city" json:"flat_city" comment:"扁平化的代理所属城市"`
	FlatDate          string         `gorm:"type:char(7);default:'';column:flat_date" json:"flat_date" comment:"扁平化时间，精确到月份，格式2020-01"`
	Channel           string         `gorm:"type:enum('agency','e_commerce','operator','special','ebag','other','mengkubao','external','aixue');default:'agency';column:channel" json:"channel" comment:"渠道类型"`
	Compound          uint8          `gorm:"type:tinyint(3) unsigned;not null;default:0;column:compound" json:"compound" comment:"是否为复合代理，即售卖多个品牌"`
	IsDirectSales     *int8          `gorm:"type:tinyint(4);default:0;column:is_direct_sales" json:"is_direct_sales,omitempty" comment:"是否直营 0非直营  1直营"`
	CreatedAt         time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at" comment:"创建时间"`
	UpdatedAt         time.Time      `gorm:"not null;default:'0000-00-00 00:00:00';column:updated_at" json:"updated_at" comment:"更新时间"`
	DeletedAt         gorm.DeletedAt `gorm:"index;column:deleted_at" json:"deleted_at,omitempty" comment:"删除时间"`
	QwPartyid         uint           `gorm:"type:int(11) unsigned;not null;default:0;column:qw_partyid" json:"qw_partyid" comment:"企业微信部门id"`
	GDP               *float64       `gorm:"type:decimal(10,2);column:gdp" json:"gdp,omitempty" comment:"上一年当地的gdp"`
	ResourceID        int            `gorm:"not null;default:0;column:resource_id" json:"resource_id" comment:"资源组"`
}

// TableName 指定表名
func (Agency) TableName() string {
	return "agency"
}
