package dto

import (
	"gorm.io/gorm"
	warrantyDto "marketing-app/internal/repository/warranty/base/dto"
	"time"
)

type Prototype struct {
	ID           int            `json:"id" `
	ModelID      int            `json:"model_id,omitempty" `
	Model        string         `json:"model,omitempty" `
	Barcode      string         `json:"barcode,omitempty" `
	Number       string         `json:"number,omitempty" `
	Imei         string         `json:"imei,omitempty" `
	TopAgency    int            `json:"top_agency,omitempty" `
	SecondAgency int            `json:"second_agency,omitempty" `
	Endpoint     int            `json:"endpoint,omitempty" `
	UserID       int            `json:"user_id,omitempty" `
	Type         int            `json:"type,omitempty" `
	Status       int            `json:"status,omitempty" `
	TabletStatus int            `json:"tablet_status,omitempty" `
	CreatedAt    *time.Time     `json:"created_at,omitempty" `
	UpdatedAt    *time.Time     `json:"updated_at,omitempty" `
	RemovedAt    gorm.DeletedAt `json:"removed_at,omitempty" `

	CategoryID int `json:"column:category_id,omitempty"`
}

type PrototypeMachineTypeConfig struct {
	Number       string `json:"number,omitempty"`
	Status       int    `json:"status,omitempty"`
	Type         int    `json:"type,omitempty"`
	Endpoint     int    `json:"endpoint,omitempty"`
	TopAgency    int    `json:"top_agency,omitempty"`
	SecondAgency int    `json:"second_agency,omitempty"`

	CategoryID   int `json:"column:category_id,omitempty"`  // machine_type表获得
	Discontinued int `json:"column:discontinued,omitempty"` // prototype_config表获得
}

type WarrantyReturn struct {
	Barcode         string     `gorm:"column:barcode"`
	ExtBarcode      string     `gorm:"column:ext_barcode"`
	Reason          string     `gorm:"column:reason"`
	ReturnAt        *time.Time `gorm:"column:return_at"`
	CreatedAt       *time.Time `gorm:"column:created_at"`
	OrderTime       *time.Time `gorm:"column:order_time"`
	EndpointName    string     `gorm:"column:endpoint_name"`
	EndpointAddress string     `gorm:"column:endpoint_address"`
	EndpointManager string     `gorm:"column:endpoint_manager"`
	ManagerPhone    string     `gorm:"column:manager_phone"`
	DataType        int        `gorm:"column:data_type"`
}

type PrototypeLimitInfo struct {
	PrototypeLimit          *int `gorm:"column:prototype_limit"`
	PrototypeFrequencyLimit *int `gorm:"column:prototype_frequency_limit"`
}

type PrototypeListItem struct {
	Barcode       string    `gorm:"column:barcode"`
	Status        int       `gorm:"column:status"`
	MachineStatus int       `gorm:"column:machine_status"`
	Model         string    `gorm:"column:model"`
	Name          string    `gorm:"column:name"`
	ID            int       `gorm:"column:id"`
	Type          int       `gorm:"column:type"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	UserName      *string   `gorm:"column:user_name"`
	Username      *string   `gorm:"column:username"`
	Phone         *string   `gorm:"column:phone"`
}

type ModelCategory struct {
	ID   int    `gorm:"column:id"`
	Name string `gorm:"column:name"`
}

type PrototypeListResult struct {
	Data  []map[string]interface{} `json:"data"`
	Size  int                      `json:"size"`
	IsEnd bool                     `json:"isEnd"`
}

type PrototypeInfo struct {
	Number       string `gorm:"column:number"`
	Status       int    `gorm:"column:status"`
	Type         int    `gorm:"column:type"`
	Endpoint     int    `gorm:"column:endpoint"`
	TopAgency    int    `gorm:"column:top_agency"`
	SecondAgency int    `gorm:"column:second_agency"`
	CategoryID   int    `gorm:"column:category_id"`
	Discontinued int    `gorm:"column:discontinued"`
}

// PrototypeWarrantyQueryResult 样机保卡查询结果，对应Python的PrototypeWarrantyQuery返回结果
type PrototypeWarrantyQueryResult struct {
	Warranty      *warrantyDto.WarrantyMachineType `json:"warranty"`       // 保卡信息
	HasWarranty   int                              `json:"has_warranty"`   // 是否有保卡：0-无保卡，1-有保卡
	HasPrototype  int                              `json:"has_prototype"`  // 是否有样机：0-无样机，1-有样机
	PrototypeData *Prototype                       `json:"prototype_data"` // 样机数据
}
