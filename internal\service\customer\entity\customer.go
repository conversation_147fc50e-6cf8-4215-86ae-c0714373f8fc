package entity

import (
	"time"
)

// CustomerListRequest 客户列表请求
type CustomerListRequest struct {
	Page     int                    // 页码，默认1
	Count    int                    // 每页数量，默认10
	Status   *uint8                 // 状态筛选
	Level    *uint8                 // 等级筛选
	Region   string                 // 地区筛选
	Industry string                 // 行业筛选
	Source   string                 // 来源筛选
	Keyword  string                 // 关键词搜索（姓名、公司、电话、邮箱）
	Filters  map[string]interface{} // 其他筛选条件
	UID      uint                   // 用户ID，从上下文获取
}

// CustomerDetailRequest 客户详情请求
type CustomerDetailRequest struct {
	CustomerID uint // 客户ID
	UID        uint // 用户ID，从上下文获取
}

// CreateCustomerRequest 创建客户请求
type CreateCustomerRequest struct {
	Name        string     // 客户姓名
	Phone       string     // 联系电话
	Email       string     // 邮箱地址
	Company     string     // 公司名称
	Position    string     // 职位
	Region      string     // 所在地区
	Industry    string     // 所属行业
	Source      string     // 客户来源
	Level       uint8      // 客户等级
	Remark      string     // 备注信息
	LastContact *time.Time // 最后联系时间
	UID         uint       // 用户ID，从上下文获取
}

// UpdateCustomerRequest 更新客户请求
type UpdateCustomerRequest struct {
	CustomerID  uint       // 客户ID
	Name        string     // 客户姓名
	Phone       string     // 联系电话
	Email       string     // 邮箱地址
	Company     string     // 公司名称
	Position    string     // 职位
	Region      string     // 所在地区
	Industry    string     // 所属行业
	Source      string     // 客户来源
	Level       uint8      // 客户等级
	Status      uint8      // 状态
	Remark      string     // 备注信息
	LastContact *time.Time // 最后联系时间
	UID         uint       // 用户ID，从上下文获取
}

// DeleteCustomerRequest 删除客户请求
type DeleteCustomerRequest struct {
	CustomerID uint // 客户ID
	UID        uint // 用户ID，从上下文获取
}

// CustomerSearchRequest 客户搜索请求
type CustomerSearchRequest struct {
	Keyword string                 // 搜索关键词
	Filters map[string]interface{} // 筛选条件
	UID     uint                   // 用户ID，从上下文获取
}

// CustomerContactListRequest 客户联系记录列表请求
type CustomerContactListRequest struct {
	CustomerID uint // 客户ID
	Page       int  // 页码，默认1
	Count      int  // 每页数量，默认10
	UID        uint // 用户ID，从上下文获取
}

// CreateCustomerContactRequest 创建客户联系记录请求
type CreateCustomerContactRequest struct {
	CustomerID uint       // 客户ID
	Type       uint8      // 联系类型：1=电话，2=邮件，3=面谈，4=其他
	Content    string     // 联系内容
	Result     string     // 联系结果
	NextTime   *time.Time // 下次联系时间
	UID        uint       // 用户ID，从上下文获取
}

// UpdateCustomerContactRequest 更新客户联系记录请求
type UpdateCustomerContactRequest struct {
	ContactID uint       // 联系记录ID
	Type      uint8      // 联系类型
	Content   string     // 联系内容
	Result    string     // 联系结果
	NextTime  *time.Time // 下次联系时间
	UID       uint       // 用户ID，从上下文获取
}

// DeleteCustomerContactRequest 删除客户联系记录请求
type DeleteCustomerContactRequest struct {
	ContactID uint // 联系记录ID
	UID       uint // 用户ID，从上下文获取
}

// CustomerFollowListRequest 客户跟进记录列表请求
type CustomerFollowListRequest struct {
	CustomerID uint // 客户ID
	Page       int  // 页码，默认1
	Count      int  // 每页数量，默认10
	UID        uint // 用户ID，从上下文获取
}

// CreateCustomerFollowRequest 创建客户跟进记录请求
type CreateCustomerFollowRequest struct {
	CustomerID uint   // 客户ID
	Stage      uint8  // 跟进阶段：1=初接触，2=需求确认，3=方案制定，4=商务谈判，5=签约成交，6=售后服务
	Priority   uint8  // 优先级：1=高，2=中，3=低
	Progress   uint8  // 进度百分比：0-100
	Content    string // 跟进内容
	UID        uint   // 用户ID，从上下文获取
}

// UpdateCustomerFollowRequest 更新客户跟进记录请求
type UpdateCustomerFollowRequest struct {
	FollowID uint   // 跟进记录ID
	Stage    uint8  // 跟进阶段
	Priority uint8  // 优先级
	Progress uint8  // 进度百分比
	Content  string // 跟进内容
	UID      uint   // 用户ID，从上下文获取
}

// DeleteCustomerFollowRequest 删除客户跟进记录请求
type DeleteCustomerFollowRequest struct {
	FollowID uint // 跟进记录ID
	UID      uint // 用户ID，从上下文获取
}

// CustomerStatisticsRequest 客户统计请求
type CustomerStatisticsRequest struct {
	StartDate *time.Time             // 开始日期
	EndDate   *time.Time             // 结束日期
	Filters   map[string]interface{} // 筛选条件
	UID       uint                   // 用户ID，从上下文获取
}

// ImportCustomersRequest 导入客户请求
type ImportCustomersRequest struct {
	Data []CreateCustomerRequest // 客户数据
	UID  uint                    // 用户ID，从上下文获取
}

// ExportCustomersRequest 导出客户请求
type ExportCustomersRequest struct {
	Filters map[string]interface{} // 筛选条件
	Format  string                 // 导出格式：excel, csv
	UID     uint                   // 用户ID，从上下文获取
}

// BatchUpdateCustomersRequest 批量更新客户请求
type BatchUpdateCustomersRequest struct {
	CustomerIDs []uint                 // 客户ID列表
	Updates     map[string]interface{} // 更新字段
	UID         uint                   // 用户ID，从上下文获取
}

// BatchDeleteCustomersRequest 批量删除客户请求
type BatchDeleteCustomersRequest struct {
	CustomerIDs []uint // 客户ID列表
	UID         uint   // 用户ID，从上下文获取
}
