package model

import (
	"time"
)

// TabletAppControl 平板应用控制表
type TabletAppControl struct {
	ID                uint      `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	DeviceID          string    `gorm:"type:varchar(100);not null;default:'';comment:设备ID" json:"device_id"`
	DeviceNumber      string    `gorm:"type:varchar(100);not null;default:'';comment:设备序列号" json:"device_number"`
	AllowUnknownApp   uint8     `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否允许安装未知应用:1=允许,0=不允许" json:"allow_unknown_app"`
	AllowNsfwUrl      uint8     `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否允许访问敏感网址:1=允许,0=不允许" json:"allow_nsfw_url"`
	ParentControlMode uint8     `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:家长控制模式:1=启用,0=禁用" json:"parent_control_mode"`
	Status            uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:状态:1=正常,0=禁用" json:"status"`
	CreatedBy         uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	UpdatedBy         uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:更新人" json:"updated_by"`
	CreatedAt         time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt         time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TabletAppControl) TableName() string {
	return "tablet_app_control"
}

// TabletBinding 平板绑定表
type TabletBinding struct {
	ID           uint       `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	DeviceID     string     `gorm:"type:varchar(100);not null;default:'';comment:设备ID" json:"device_id"`
	DeviceNumber string     `gorm:"type:varchar(100);not null;default:'';comment:设备序列号" json:"device_number"`
	DeviceType   uint8      `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:设备类型:1=平板,2=手表,3=其他" json:"device_type"`
	ParentPhone  string     `gorm:"type:varchar(20);not null;default:'';comment:家长手机号" json:"parent_phone"`
	ParentUserID uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:家长用户ID" json:"parent_user_id"`
	StudentName  string     `gorm:"type:varchar(50);not null;default:'';comment:学生姓名" json:"student_name"`
	StudentID    uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:学生ID" json:"student_id"`
	BindingCode  string     `gorm:"type:varchar(100);not null;default:'';comment:绑定码" json:"binding_code"`
	BindingType  uint8      `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:绑定类型:1=家长助手,2=手表绑定,3=其他" json:"binding_type"`
	Status       uint8      `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:绑定状态:1=已绑定,0=已解绑" json:"status"`
	BindTime     time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:绑定时间" json:"bind_time"`
	UnbindTime   *time.Time `gorm:"type:timestamp;comment:解绑时间" json:"unbind_time"`
	CreatedBy    uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	UpdatedBy    uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:更新人" json:"updated_by"`
	CreatedAt    time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TabletBinding) TableName() string {
	return "tablet_binding"
}

// TabletPhoneVerification 平板手机验证表
type TabletPhoneVerification struct {
	ID           uint       `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	DeviceID     string     `gorm:"type:varchar(100);not null;default:'';comment:设备ID" json:"device_id"`
	DeviceNumber string     `gorm:"type:varchar(100);not null;default:'';comment:设备序列号" json:"device_number"`
	Phone        string     `gorm:"type:varchar(20);not null;default:'';comment:手机号" json:"phone"`
	VerifyCode   string     `gorm:"type:varchar(10);not null;default:'';comment:验证码" json:"verify_code"`
	VerifyType   uint8      `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:验证类型:1=应用安装,2=网址访问,3=解绑设备,4=其他" json:"verify_type"`
	Status       uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:验证状态:1=已验证,0=未验证" json:"status"`
	ExpireTime   time.Time  `gorm:"type:timestamp;not null;comment:过期时间" json:"expire_time"`
	VerifyTime   *time.Time `gorm:"type:timestamp;comment:验证时间" json:"verify_time"`
	CreatedAt    time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TabletPhoneVerification) TableName() string {
	return "tablet_phone_verification"
}

// TabletAppWhitelist 平板应用白名单表
type TabletAppWhitelist struct {
	ID           uint      `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	DeviceID     string    `gorm:"type:varchar(100);not null;default:'';comment:设备ID" json:"device_id"`
	DeviceNumber string    `gorm:"type:varchar(100);not null;default:'';comment:设备序列号" json:"device_number"`
	AppName      string    `gorm:"type:varchar(100);not null;default:'';comment:应用名称" json:"app_name"`
	AppPackage   string    `gorm:"type:varchar(200);not null;default:'';comment:应用包名" json:"app_package"`
	AppVersion   string    `gorm:"type:varchar(50);not null;default:'';comment:应用版本" json:"app_version"`
	AllowInstall uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:是否允许安装:1=允许,0=禁止" json:"allow_install"`
	Status       uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:状态:1=正常,0=禁用" json:"status"`
	CreatedBy    uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	UpdatedBy    uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:更新人" json:"updated_by"`
	CreatedAt    time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TabletAppWhitelist) TableName() string {
	return "tablet_app_whitelist"
}

// TabletUrlBlacklist 平板网址黑名单表
type TabletUrlBlacklist struct {
	ID           uint      `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	DeviceID     string    `gorm:"type:varchar(100);not null;default:'';comment:设备ID" json:"device_id"`
	DeviceNumber string    `gorm:"type:varchar(100);not null;default:'';comment:设备序列号" json:"device_number"`
	Url          string    `gorm:"type:text;not null;comment:网址URL" json:"url"`
	UrlType      uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:网址类型:1=敏感内容,2=社交网站,3=游戏网站,4=其他" json:"url_type"`
	BlockReason  string    `gorm:"type:varchar(200);not null;default:'';comment:屏蔽原因" json:"block_reason"`
	Status       uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:状态:1=正常,0=禁用" json:"status"`
	CreatedBy    uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	UpdatedBy    uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:更新人" json:"updated_by"`
	CreatedAt    time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TabletUrlBlacklist) TableName() string {
	return "tablet_url_blacklist"
}
