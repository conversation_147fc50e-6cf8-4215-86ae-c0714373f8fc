package salesstat

import (
	"encoding/json"
	"fmt"
	"io"
	"marketing-app/internal/api/client/salesstat/config"
	"marketing-app/internal/pkg/log"
	"net/http"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type SalesStatClient struct {
	httpClient *http.Client
	cfg        *config.AppConfig
}

func NewSalesStatClient(cfg *config.AppConfig) SalesStatClient {
	return SalesStatClient{
		httpClient: &http.Client{
			Timeout: cfg.HTTPTimeout,
		},
		cfg: cfg,
	}
}

// GetSalesStat 获取销售统计数据
func (s *SalesStatClient) GetSalesStat(c *gin.Context, accessToken string) (map[string]interface{}, error) {
	// 构建请求URL
	requestURL := s.cfg.Host + "/admin-api/v1/statistics/warranty-card/same-term-analyze"

	// 创建GET请求
	req, err := http.NewRequestWithContext(c, http.MethodGet, requestURL, nil)
	if err != nil {
		err = errors.Wrap(err, "创建请求失败")
		return nil, err
	}

	// 设置请求头
	req.Header.Set("X-Access-Token", accessToken)
	req.Header.Set("X-Token-Origin", "care_app")

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		err = errors.Wrap(err, "调用销售统计API失败")
		log.Error("sales stat send error", zap.Error(err))
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("销售统计API响应错误，状态码: %d", resp.StatusCode))
		log.Error("sales stat response error", zap.Int("status_code", resp.StatusCode))
		return nil, err
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		err = errors.Wrap(err, "读取响应失败")
		return nil, err
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		err = errors.Wrap(err, "解析响应失败")
		return nil, err
	}

	log.Info("sales stat result", zap.Any("result", result))

	// 检查响应状态
	if code, ok := result["code"].(float64); ok && code == 0 {
		if data, ok := result["data"]; ok {
			return map[string]interface{}{
				"data": data,
			}, nil
		}
	} else {
		log.Error("sales stat response error", zap.Any("response", result))
	}

	// 如果API调用失败或无数据，返回空数据（不返回错误，与Python版本一致）
	return map[string]interface{}{
		"data": map[string]interface{}{},
	}, nil
}
