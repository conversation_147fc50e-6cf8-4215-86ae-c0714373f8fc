package model

import (
	"time"
)

// MachineType 机型表
type MachineType struct {
	Id                int     `json:"id" gorm:"column:id"`                                   // id
	Name              string  `json:"name" gorm:"column:name"`                               // 包装机型名称,读取的是MES的外箱打印名称(print_name)
	NavName           string  `json:"nav_name" gorm:"column:nav_name"`                       // 显示机型名字，因为部分name不同的需要合并显示
	ModelId           int     `json:"model_id" gorm:"column:model_id"`                       // 机型编号,读取的是MES的机型编号(id)
	ModelName         string  `json:"model_name" gorm:"column:model_name"`                   // 机器系统内部的机型名称,平板的一般都以Readboy_开头
	BillModels        string  `json:"bill_models" gorm:"column:bill_models"`                 // 实际对应的出货单机型名称，多个逗号隔开
	CategoryId        int     `json:"category_id" gorm:"column:category_id"`                 // 类型id，关联到model_category表
	Series            int     `json:"series" gorm:"column:series"`                           // 0：其它；5：平板；7：手表；8：幼教平板
	Market            string  `json:"market" gorm:"column:market"`                           // 上市日期
	CustomerPrice     float64 `json:"customer_price" gorm:"column:customer_price"`           // 顾客价格
	Level             int     `json:"level" gorm:"column:level"`                             // 产品等级：0-旧款，1-低端(0-2500元)，2-中低端(2500-3000元)，3-中高端(3000-4000元)，4-高端(4000元以上)
	ExtBarcodeNum     int     `json:"ext_barcode_num" gorm:"column:ext_barcode_num"`         // 附加条码个数，默认为0表示无附加条码
	Visibility        int     `json:"visibility" gorm:"column:visibility"`                   // 可见性,0是隐藏,1是可见
	ChartShow         int     `json:"chart_show" gorm:"column:chart_show"`                   // chart实销统计后台是否显示该机型，1-是，0-否
	Sort              int     `json:"sort" gorm:"column:sort"`                               // chart实销统计后台界面显示排序
	Other             int     `json:"other" gorm:"column:other"`                             // chart实销统计后台是否统计为其他类别，1-是，0-否
	ECommerce         int     `json:"e_commerce" gorm:"column:e_commerce"`                   // 是否为电商机型，1-是，0-否
	Assessment        int     `json:"assessment" gorm:"column:assessment"`                   // 是否纳入考核：1-是，0-否
	Declare           int     `json:"declare" gorm:"column:declare"`                         // 是否允许申报 0--否 1--是
	Stock             int     `json:"stock" gorm:"column:stock"`                             // 是否允许备货 0--否 1--是
	BoxCnt            int     `json:"box_cnt" gorm:"column:box_cnt"`                         // 包装数量
	InstockCnt        int     `json:"instock_cnt" gorm:"column:instock_cnt"`                 // 金蝶入库数量
	OutstockCnt       int     `json:"outstock_cnt" gorm:"column:outstock_cnt"`               // 金蝶出库数量
	PrototypeStatus   int     `json:"prototype_status" gorm:"column:prototype_status"`       // 样机状态：0-不可设为样机；1-可以
	PrototypeApkPath  string  `json:"prototype_apk_path" gorm:"column:prototype_apk_path"`   // 样机apk下载路径
	VersionCode       string  `json:"version_code" gorm:"column:version_code"`               // apk版本号
	CompanyPrice      float64 `json:"company_price" gorm:"column:company_price"`             // 公司价
	TopAgencyPrice    float64 `json:"top_agency_price" gorm:"column:top_agency_price"`       // 总代价格
	SecondAgencyPrice float64 `json:"second_agency_price" gorm:"column:second_agency_price"` // 二代价格
}

func (MachineType) TableName() string {
	return "machine_type"
}

// RepairMachineType 维修机型表
type RepairMachineType struct {
	Id           int       `json:"id" gorm:"column:id"`                       // id
	ModelId      int       `json:"model_id" gorm:"column:model_id"`           // 机型id
	ModelName    string    `json:"model_name" gorm:"column:model_name"`       // 固件机型
	CategoryId   int       `json:"category_id" gorm:"column:category_id"`     // 分类id
	CategoryName string    `json:"category_name" gorm:"column:category_name"` // 分类名称
	Visibility   int       `json:"visibility" gorm:"column:visibility"`       // 是否隐藏 0:隐藏 1:不隐藏
	CreatedAt    time.Time `json:"-" gorm:"created_at"`                       // CreatedAt 创建时间
	CreateTime   string    `json:"create_time" gorm:"-"`                      // 创建时间
}

func (RepairMachineType) TableName() string {
	return "machine_type"
}
