package training

import (
	"crypto/cipher"
	"crypto/des"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/repository/training"
	repoDto "marketing-app/internal/repository/training/dto"
	"marketing-app/internal/service/training/entity"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TrainingSvc interface {
	GetTrainVideos(c *gin.Context, req *entity.TrainVideosRequest) (*repoDto.TrainVideosResponse, error)
	GetTrainList(c *gin.Context, req *entity.TrainListRequest) (*repoDto.TrainListResponse, error)
	GetTrainTypes(c *gin.Context) ([]repoDto.TrainTypeItem, error)
	GetTrainCategories(c *gin.Context, req *entity.TrainCategoriesRequest) ([]repoDto.TrainCategoryItem, error)
	GetTrainTypeCategories(c *gin.Context) ([]repoDto.TrainTypeCategoryItem, error)
	AddLearningHistory(c *gin.Context, req *entity.AddLearningHistoryRequest) error
	AddShareHistory(c *gin.Context, req *entity.AddShareHistoryRequest) (int, error)
	SearchTrains(c *gin.Context, req *entity.TrainSearchRequest) ([]repoDto.TrainSearchItem, error)
	GetTrainHot(c *gin.Context, req *entity.TrainHotRequest) (*repoDto.TrainHotResponse, error)
	AddCollection(c *gin.Context, req *entity.AddCollectionRequest) error
	CancelCollection(c *gin.Context, req *entity.CancelCollectionRequest) error
	GetCollectionList(c *gin.Context, req *entity.CollectionListRequest) (*repoDto.CollectionListResponse, error)
	GetTrainBanner(c *gin.Context, req *entity.TrainBannerRequest) ([]repoDto.TrainBannerItem, error)
}

type trainingSvc struct {
	trainingRepo training.Training
}

func NewTrainingService(trainingRepo training.Training) TrainingSvc {
	return &trainingSvc{
		trainingRepo: trainingRepo,
	}
}

func (t *trainingSvc) GetTrainVideos(c *gin.Context, req *entity.TrainVideosRequest) (*repoDto.TrainVideosResponse, error) {
	// 设置默认值
	if req.Count <= 0 {
		req.Count = 10
	}

	// 调用repository层获取训练视频
	repoResult, err := t.trainingRepo.GetTrainVideos(c, req.Ts, req.Count)
	if err != nil {
		return nil, errors.Wrap(err, "查询训练视频失败")
	}

	// 如果没有找到数据，返回nil
	if repoResult == nil {
		return nil, nil
	}

	// 处理预览图和视频路径URL
	for i := range repoResult.Data {
		// 处理预览图
		if repoResult.Data[i].Preview != "" {
			previews := t.processPreviewImages(repoResult.Data[i].Preview)
			if previews != nil {
				// 将处理后的预览图数组转换为JSON字符串存储
				previewsJSON, _ := json.Marshal(previews)
				repoResult.Data[i].Preview = string(previewsJSON)
			} else {
				repoResult.Data[i].Preview = ""
			}
		}

		// 处理视频路径
		if repoResult.Data[i].Path != "" {
			repoResult.Data[i].Path = config.GetString("oss.cname") + repoResult.Data[i].Path
		}
	}

	return repoResult, nil
}

// processPreviewImages 处理预览图数组，添加OSS域名前缀
func (t *trainingSvc) processPreviewImages(preview string) []string {
	if preview == "" {
		return nil
	}

	var previews []string
	if err := json.Unmarshal([]byte(preview), &previews); err != nil {
		return nil
	}

	if len(previews) == 0 {
		return nil
	}

	ossCname := config.GetString("oss.cname")
	var processedPreviews []string
	for _, pre := range previews {
		if pre != "" {
			// 确保URL不重复添加域名前缀
			if !strings.HasPrefix(pre, "http") {
				processedPreviews = append(processedPreviews, ossCname+pre)
			} else {
				processedPreviews = append(processedPreviews, pre)
			}
		}
	}

	return processedPreviews
}

func (t *trainingSvc) GetTrainList(c *gin.Context, req *entity.TrainListRequest) (*repoDto.TrainListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Count <= 0 {
		req.Count = 10
	}
	if req.Type == 0 {
		req.Type = -1
	}
	if req.Category == 0 {
		req.Category = -1
	}
	if req.Order <= 0 {
		req.Order = 1
	}

	// 调用repository层获取培训列表
	repoResult, err := t.trainingRepo.GetTrainList(c, req.Page, req.Count, req.Type, req.Category, req.Order, req.UID)
	if err != nil {
		return nil, errors.Wrap(err, "查询培训列表失败")
	}

	// 处理每个培训项的数据
	for i := range repoResult.Data {
		// 处理预览图
		if repoResult.Data[i].Preview != "" {
			previews := t.processPreviewImages(repoResult.Data[i].Preview)
			if previews != nil {
				// 将处理后的预览图数组转换为JSON字符串存储
				previewsJSON, _ := json.Marshal(previews)
				repoResult.Data[i].Preview = string(previewsJSON)
			} else {
				repoResult.Data[i].Preview = ""
			}
		}

		// 处理视频路径
		if repoResult.Data[i].Path != "" {
			repoResult.Data[i].Path = config.GetString("oss.cname") + repoResult.Data[i].Path
		}

		// 处理分享URL
		if repoResult.Data[i].Share == 1 {
			repoResult.Data[i].ShareURL = t.generateShareURL(repoResult.Data[i].ID)
		} else {
			repoResult.Data[i].ShareURL = ""
		}
	}

	return repoResult, nil
}

// generateShareURL 生成分享URL（模拟Python版本的share函数）
func (t *trainingSvc) generateShareURL(id uint) string {
	const (
		shareBaseURL = "http://yx.readboy.com/external/train/share/"
		secretKey    = "YOzOtLA0"
		iv           = "ACCLd6Hi"
	)

	// 创建DES加密器
	block, err := des.NewCipher([]byte(secretKey))
	if err != nil {
		return ""
	}

	// 使用CBC模式
	mode := cipher.NewCBCEncrypter(block, []byte(iv))

	// 准备数据进行PKCS5填充
	data := []byte(fmt.Sprintf("%d", id))
	data = t.pkcs5Padding(data, block.BlockSize())

	// 加密
	encrypted := make([]byte, len(data))
	mode.CryptBlocks(encrypted, data)

	// 转换为十六进制字符串
	hexStr := hex.EncodeToString(encrypted)

	return shareBaseURL + hexStr
}

// pkcs5Padding PKCS5填充
func (t *trainingSvc) pkcs5Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

func (t *trainingSvc) GetTrainTypes(c *gin.Context) ([]repoDto.TrainTypeItem, error) {
	// 调用repository层获取培训类型
	result, err := t.trainingRepo.GetTrainTypes(c)
	if err != nil {
		return nil, errors.Wrap(err, "查询培训类型失败")
	}

	return result, nil
}

func (t *trainingSvc) GetTrainCategories(c *gin.Context, req *entity.TrainCategoriesRequest) ([]repoDto.TrainCategoryItem, error) {
	// 调用repository层获取培训分类
	result, err := t.trainingRepo.GetTrainCategories(c, req.Type)
	if err != nil {
		return nil, errors.Wrap(err, "查询培训分类失败")
	}

	return result, nil
}

func (t *trainingSvc) GetTrainTypeCategories(c *gin.Context) ([]repoDto.TrainTypeCategoryItem, error) {
	// 调用repository层获取培训类型分类关系
	result, err := t.trainingRepo.GetTrainTypeCategories(c)
	if err != nil {
		return nil, errors.Wrap(err, "查询培训类型分类关系失败")
	}

	return result, nil
}

func (t *trainingSvc) AddLearningHistory(c *gin.Context, req *entity.AddLearningHistoryRequest) error {
	// 参数验证
	if req.ID == 0 {
		return errors.New("培训ID不能为空")
	}
	if req.LearningTime == "" {
		return errors.New("学习时长不能为空")
	}
	if req.UID == 0 {
		return errors.New("用户ID不能为空")
	}

	// 调用repository层添加学习历史
	if err := t.trainingRepo.AddLearningHistory(c, req.ID, req.UID, req.LearningTime); err != nil {
		return errors.Wrap(err, "添加学习历史失败")
	}

	return nil
}

func (t *trainingSvc) AddShareHistory(c *gin.Context, req *entity.AddShareHistoryRequest) (int, error) {
	// 参数验证
	if req.TrainID == 0 {
		return 0, errors.New("培训ID不能为空")
	}
	if req.UID == 0 {
		return 0, errors.New("用户ID不能为空")
	}

	// 调用repository层添加分享历史
	result, err := t.trainingRepo.AddShareHistory(c, req.TrainID, req.UID)
	if err != nil {
		return 0, errors.Wrap(err, "添加分享历史失败")
	}

	return result, nil
}

func (t *trainingSvc) SearchTrains(c *gin.Context, req *entity.TrainSearchRequest) ([]repoDto.TrainSearchItem, error) {
	// 参数验证
	if req.Keyword == "" {
		return nil, errors.New("搜索关键词不能为空")
	}
	if req.UID == 0 {
		return nil, errors.New("用户ID不能为空")
	}

	// 调用repository层搜索培训
	repoResult, err := t.trainingRepo.SearchTrains(c, req.Keyword, req.UID)
	if err != nil {
		return nil, errors.Wrap(err, "搜索培训失败")
	}

	// 处理每个搜索结果的数据（与Python版本保持一致）
	for i := range repoResult {
		// 处理预览图
		if repoResult[i].Preview != "" {
			previews := t.processPreviewImages(repoResult[i].Preview)
			if previews != nil {
				// 将处理后的预览图数组转换为JSON字符串存储
				previewsJSON, _ := json.Marshal(previews)
				repoResult[i].Preview = string(previewsJSON)
			} else {
				repoResult[i].Preview = ""
			}
		}

		// 处理视频路径
		if repoResult[i].Path != "" {
			repoResult[i].Path = config.GetString("oss.cname") + repoResult[i].Path
		}

		// 处理分享URL
		if repoResult[i].Share == 1 {
			repoResult[i].ShareURL = t.generateShareURL(repoResult[i].ID)
		} else {
			repoResult[i].ShareURL = ""
		}
	}

	return repoResult, nil
}

func (t *trainingSvc) GetTrainHot(c *gin.Context, req *entity.TrainHotRequest) (*repoDto.TrainHotResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Count <= 0 {
		req.Count = 10
	}

	// 调用repository层获取热门培训
	repoResult, err := t.trainingRepo.GetTrainHot(c, req.Page, req.Count)
	if err != nil {
		return nil, errors.Wrap(err, "查询热门培训失败")
	}

	// 如果没有找到数据，返回nil
	if repoResult == nil {
		return nil, nil
	}

	// 处理每个热门培训的数据（与Python版本保持一致）
	for i := range repoResult.Data {
		// 处理预览图
		if repoResult.Data[i].Preview != "" {
			previews := t.processPreviewImages(repoResult.Data[i].Preview)
			if previews != nil {
				// 将处理后的预览图数组转换为JSON字符串存储
				previewsJSON, _ := json.Marshal(previews)
				repoResult.Data[i].Preview = string(previewsJSON)
			} else {
				repoResult.Data[i].Preview = ""
			}
		}

		// 处理视频路径
		if repoResult.Data[i].Path != "" {
			repoResult.Data[i].Path = config.GetString("oss.cname") + repoResult.Data[i].Path
		}

		// 处理分享URL
		if repoResult.Data[i].Share == 1 {
			repoResult.Data[i].ShareURL = t.generateShareURL(repoResult.Data[i].ID)
		} else {
			repoResult.Data[i].ShareURL = ""
		}
	}

	return repoResult, nil
}

func (t *trainingSvc) AddCollection(c *gin.Context, req *entity.AddCollectionRequest) error {
	// 参数验证
	if req.TrainID == 0 {
		return errors.New("培训ID不能为空")
	}
	if req.UID == 0 {
		return errors.New("用户ID不能为空")
	}

	// 调用repository层添加收藏
	if err := t.trainingRepo.AddCollection(c, req.TrainID, req.UID); err != nil {
		return errors.Wrap(err, "添加收藏失败")
	}

	return nil
}

func (t *trainingSvc) CancelCollection(c *gin.Context, req *entity.CancelCollectionRequest) error {
	// 参数验证
	if req.TrainID == 0 {
		return errors.New("培训ID不能为空")
	}
	if req.UID == 0 {
		return errors.New("用户ID不能为空")
	}

	// 调用repository层取消收藏
	if err := t.trainingRepo.CancelCollection(c, req.TrainID, req.UID); err != nil {
		return errors.Wrap(err, "取消收藏失败")
	}

	return nil
}

func (t *trainingSvc) GetCollectionList(c *gin.Context, req *entity.CollectionListRequest) (*repoDto.CollectionListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Count <= 0 {
		req.Count = 10
	}
	if req.UID == 0 {
		return nil, errors.New("用户ID不能为空")
	}

	// 调用repository层获取收藏列表
	repoResult, err := t.trainingRepo.GetCollectionList(c, req.UID, req.Page, req.Count)
	if err != nil {
		return nil, errors.Wrap(err, "查询收藏列表失败")
	}

	// 处理每个收藏项的数据
	for i := range repoResult.Data {
		// 处理预览图
		if repoResult.Data[i].Preview != "" {
			previews := t.processPreviewImages(repoResult.Data[i].Preview)
			if previews != nil {
				// 将处理后的预览图数组转换为JSON字符串存储
				previewsJSON, _ := json.Marshal(previews)
				repoResult.Data[i].Preview = string(previewsJSON)
			} else {
				repoResult.Data[i].Preview = ""
			}
		}

		// 处理视频路径
		if repoResult.Data[i].Path != "" {
			repoResult.Data[i].Path = config.GetString("oss.cname") + repoResult.Data[i].Path
		}

		// 处理分享URL
		if repoResult.Data[i].Share == 1 {
			repoResult.Data[i].ShareURL = t.generateShareURL(repoResult.Data[i].ID)
		} else {
			repoResult.Data[i].ShareURL = ""
		}
	}

	return repoResult, nil
}

func (t *trainingSvc) GetTrainBanner(c *gin.Context, req *entity.TrainBannerRequest) ([]repoDto.TrainBannerItem, error) {
	// 参数验证
	if req.Type == -1 {
		return nil, errors.New("种类不能为空")
	}
	if req.UID == 0 {
		return nil, errors.New("用户ID不能为空")
	}

	// 调用repository层获取Banner列表
	repoResult, err := t.trainingRepo.GetTrainBanner(c, req.Type, req.UID)
	if err != nil {
		return nil, errors.Wrap(err, "查询Banner列表失败")
	}

	// 处理每个Banner项的数据（与Python版本保持一致）
	for i := range repoResult {
		// 处理预览图
		if repoResult[i].Preview != "" {
			previews := t.processPreviewImages(repoResult[i].Preview)
			if previews != nil {
				// 将处理后的预览图数组转换为JSON字符串存储
				previewsJSON, _ := json.Marshal(previews)
				repoResult[i].Preview = string(previewsJSON)
			} else {
				repoResult[i].Preview = ""
			}
		}

		// 处理视频路径
		if repoResult[i].Path != "" {
			repoResult[i].Path = config.GetString("oss.cname") + repoResult[i].Path
		}

		// 处理Banner图片
		if repoResult[i].BannerImage != "" {
			repoResult[i].BannerImage = config.GetString("oss.cname") + repoResult[i].BannerImage
		}

		// 处理分享URL
		if repoResult[i].Share == 1 {
			repoResult[i].ShareURL = t.generateShareURL(repoResult[i].ID)
		} else {
			repoResult[i].ShareURL = ""
		}
	}

	return repoResult, nil
}
