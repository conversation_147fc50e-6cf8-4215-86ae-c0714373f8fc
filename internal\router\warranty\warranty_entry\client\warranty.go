package client

type CreateWarrantyRequest struct {
	Uid              int    `json:"uid"`
	Barcode          string `json:"barcode" binding:"required"`
	BuyDate          string `json:"buy_date" binding:"required"`
	CustomerPhone    string `json:"customer_phone" binding:"required"`
	CustomerName     string `json:"customer_name" binding:"required"`
	CustomerSex      string `json:"customer_sex" binding:"required"`
	PurchaseWay      string `json:"purchase_way" binding:"required"`
	Salesman         string `json:"salesman" binding:"required"`
	StudentName      string `json:"student_name,omitempty"`
	StudentGrade     string `json:"student_grade,omitempty"`
	StudentSchool    string `json:"student_school,omitempty"`
	StudentBirthday  string `json:"student_birthday,omitempty"`
	StudentSex       string `json:"student_sex,omitempty"`
	Recommender      string `json:"recommender,omitempty"`
	RecommenderPhone string `json:"recommender_phone,omitempty"`
}
