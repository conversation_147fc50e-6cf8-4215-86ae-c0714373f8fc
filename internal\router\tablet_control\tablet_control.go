package tablet_control

import (
	"marketing-app/internal/handler/tablet_control"
	"marketing-app/internal/pkg/db"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	tabletControlRepo "marketing-app/internal/repository/tablet_control"
	tabletControlSvc "marketing-app/internal/service/tablet_control"

	"github.com/gin-gonic/gin"
)

type TabletControlRouter interface {
	Register(r *gin.RouterGroup)
}

type tabletControlRouter struct{}

func NewTabletControlRouter() TabletControlRouter {
	return &tabletControlRouter{}
}

func (tcr *tabletControlRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	database, _ := db.GetDB()

	// 初始化各层组件
	tabletControlRepository := tabletControlRepo.NewTabletControl(database)
	prototypeRepository := prototypeRepo.NewPrototypeRepo(database)
	machineRepository := machineRepo.NewMachine(database)
	tabletControlService := tabletControlSvc.NewTabletControl(tabletControlRepository, prototypeRepository, machineRepository)
	tabletControlHandler := tablet_control.NewTabletControlHandler(tabletControlService)

	r.POST("/allow_unknown_app", tabletControlHandler.AllowUnknownApp)  // 设置允许未知应用安装状态
	r.GET("/allow_unknown_app", tabletControlHandler.AllowUnknownApp)   // 验证手机验证码
	r.GET("/get_allow_nsfw_url", tabletControlHandler.GetAllowNsfwUrl)  // 获取设备NSFW URL允许状态
	r.POST("/set_allow_nsfw_url", tabletControlHandler.SetAllowNsfwUrl) // 设置设备NSFW URL允许状态
	r.GET("/cancel_bindings", tabletControlHandler.CancelBindings)      // 取消设备绑定
	r.POST("/unbind_watch", tabletControlHandler.UnbindWatch)           // 解绑手表
}
