package model

import (
	"time"
)

// EndpointChannel 终端渠道类型模型
type EndpointChannel struct {
	ID        uint       `gorm:"primaryKey;autoIncrement;column:id" json:"id" comment:"主键"`
	Name      string     `gorm:"type:varchar(50);not null;column:name" json:"name" comment:"渠道名称"`
	CreatedAt time.Time  `gorm:"not null;default:'0000-00-00 00:00:00';column:created_at" json:"created_at"`
	UpdatedAt *time.Time `gorm:"column:updated_at" json:"updated_at,omitempty"`
}

// TableName 指定表名
func (EndpointChannel) TableName() string {
	return "endpoint_channel"
}
