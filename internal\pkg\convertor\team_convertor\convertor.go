package team_convertor

import (
	handlerDTO "marketing-app/internal/handler/team/dto"
	"marketing-app/internal/service/team/entity"
)

type TeamInfoConvertor struct{}

func NewTeamInfoConvertor() *TeamInfoConvertor {
	return &TeamInfoConvertor{}
}

// ToEntity 将uid转换为entity
func (c *TeamInfoConvertor) ToEntity(uid uint) *entity.TeamInfoEntity {
	return &entity.TeamInfoEntity{
		UID: uid,
	}
}

// EntityToDTO 将entity列表转换为handler DTO列表
func (c *TeamInfoConvertor) EntityToDTO(entities []entity.TeamMemberEntity) []handlerDTO.TeamMember {
	result := make([]handlerDTO.TeamMember, 0, len(entities))

	for _, e := range entities {
		member := handlerDTO.TeamMember{
			ID:            int(e.ID),
			Username:      e.Username,
			Avatar:        e.Avatar,
			Name:          e.Name,
			ExtendName:    e.ExtendName,
			ExtendPhone:   e.Extend<PERSON>hone,
			WechatID:      e.WechatID,
			WechatImage:   e.WechatImage,
			QrCodeAddress: e.QrCodeAddress,
			Role:          e.Role,
			Status:        e.Status,
		}
		result = append(result, member)
	}

	return result
}
