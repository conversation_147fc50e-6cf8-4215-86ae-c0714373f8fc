package model

import (
	"time"

	"gorm.io/gorm"
)

// SalesPromotionList 促销活动列表
type SalesPromotionList struct {
	Id               int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SalesPromotionId int        `gorm:"column:sales_promotion_id;not null" json:"sales_promotion_id"`
	Endpoint         int        `gorm:"column:endpoint;not null" json:"endpoint"`
	ModelId          int        `gorm:"column:model_id;not null" json:"model_id"`
	Barcode          string     `gorm:"column:barcode;type:varchar(255);not null" json:"barcode"`
	BuyDate          *time.Time `gorm:"column:buy_date" json:"buy_date"`
	WarrantyId       int        `gorm:"column:warranty_id;not null" json:"warranty_id"`
	IsReceipt        int        `gorm:"column:is_receipt;default:0" json:"is_receipt"`
	ReceiptAt        *time.Time `gorm:"column:receipt_at" json:"receipt_at"`
	CreatedAt        time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt        *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (SalesPromotionList) TableName() string {
	return "sales_promotion_list"
}

// SalesPromotionListReceipt 促销活动列表回执
type SalesPromotionListReceipt struct {
	Id                   int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SalesPromotionListId int            `gorm:"column:sales_promotion_list_id;not null" json:"sales_promotion_list_id"`
	Receipt              string         `gorm:"column:receipt;type:varchar(500);not null" json:"receipt"`
	Number               string         `gorm:"column:number;type:varchar(100)" json:"number"`
	Type                 int            `gorm:"column:type;default:1" json:"type"`
	CreatedAt            time.Time      `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	DeleteAt             gorm.DeletedAt `gorm:"column:delete_at" json:"delete_at"`
}

func (SalesPromotionListReceipt) TableName() string {
	return "sales_promotion_list_receipt"
}

// SalesPromotion 促销活动
type SalesPromotion struct {
	Id         int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name       string     `gorm:"column:name;type:varchar(255);not null" json:"name"`
	ReceiptDay *time.Time `gorm:"column:receipt_day" json:"receipt_day"`
	CreatedAt  time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt  *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (SalesPromotion) TableName() string {
	return "sales_promotion"
}
