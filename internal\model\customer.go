package model

import (
	"time"
)

// Customer 客户信息表
type Customer struct {
	ID          uint       `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	Name        string     `gorm:"type:varchar(100);not null;default:'';comment:客户姓名" json:"name"`
	Phone       string     `gorm:"type:varchar(20);not null;default:'';comment:联系电话" json:"phone"`
	Email       string     `gorm:"type:varchar(100);not null;default:'';comment:邮箱地址" json:"email"`
	Company     string     `gorm:"type:varchar(200);not null;default:'';comment:公司名称" json:"company"`
	Position    string     `gorm:"type:varchar(50);not null;default:'';comment:职位" json:"position"`
	Region      string     `gorm:"type:varchar(100);not null;default:'';comment:所在地区" json:"region"`
	Industry    string     `gorm:"type:varchar(100);not null;default:'';comment:所属行业" json:"industry"`
	Source      string     `gorm:"type:varchar(50);not null;default:'';comment:客户来源" json:"source"`
	Level       uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:客户等级" json:"level"`
	Status      uint8      `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:状态：1=正常，0=禁用" json:"status"`
	Remark      string     `gorm:"type:text;comment:备注信息" json:"remark"`
	CreatedBy   uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	UpdatedBy   uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:更新人" json:"updated_by"`
	CreatedAt   time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
	DeletedAt   *time.Time `gorm:"type:timestamp;comment:删除时间" json:"deleted_at"`
	LastContact *time.Time `gorm:"type:timestamp;comment:最后联系时间" json:"last_contact"`
}

func (Customer) TableName() string {
	return "customer"
}

// CustomerContact 客户联系记录表
type CustomerContact struct {
	ID         uint       `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	CustomerID uint       `gorm:"type:int(10) unsigned;not null;comment:客户ID" json:"customer_id"`
	Type       uint8      `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:联系类型：1=电话，2=邮件，3=面谈，4=其他" json:"type"`
	Content    string     `gorm:"type:text;not null;comment:联系内容" json:"content"`
	Result     string     `gorm:"type:text;comment:联系结果" json:"result"`
	NextTime   *time.Time `gorm:"type:timestamp;comment:下次联系时间" json:"next_time"`
	CreatedBy  uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	CreatedAt  time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
}

func (CustomerContact) TableName() string {
	return "customer_contact"
}

// CustomerFollow 客户跟进表
type CustomerFollow struct {
	ID         uint      `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	CustomerID uint      `gorm:"type:int(10) unsigned;not null;comment:客户ID" json:"customer_id"`
	Stage      uint8     `gorm:"type:tinyint(3) unsigned;not null;default:1;comment:跟进阶段：1=初接触，2=需求确认，3=方案制定，4=商务谈判，5=签约成交，6=售后服务" json:"stage"`
	Priority   uint8     `gorm:"type:tinyint(3) unsigned;not null;default:2;comment:优先级：1=高，2=中，3=低" json:"priority"`
	Progress   uint8     `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:进度百分比：0-100" json:"progress"`
	Content    string    `gorm:"type:text;not null;comment:跟进内容" json:"content"`
	CreatedBy  uint      `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
	CreatedAt  time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
}

func (CustomerFollow) TableName() string {
	return "customer_follow"
}
