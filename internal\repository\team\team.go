package team

import (
	"marketing-app/internal/repository/team/dto"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TeamRepo interface {
	// GetManagerList 获取店长和代理店长列表
	GetManagerList(c *gin.Context, endpointID int) ([]dto.TeamMember, error)
	// GetSalesclerkList 获取店员列表
	GetSalesclerkList(c *gin.Context, endpointID int) ([]dto.TeamMember, error)
}

type teamRepo struct {
	db *gorm.DB
}

func NewTeamRepo(db *gorm.DB) TeamRepo {
	return &teamRepo{db: db}
}

// GetManagerList 获取店长和代理店长列表（manager和agency_manager角色）
func (r *teamRepo) GetManagerList(c *gin.Context, endpointID int) ([]dto.TeamMember, error) {
	var results []dto.TeamMember

	err := r.db.WithContext(c).
		Select(`u.id, u.username, u.avatar, 
				ued.name, ued.name AS extend_name, 
				ued.phone AS extend_phone, ued.wechat_id, ued.wechat_image, ued.qr_code_address,
				ue.role, u.status`).
		Table("admin_users u").
		Joins("RIGHT JOIN user_endpoint ue ON u.id = ue.uid").
		Joins("LEFT JOIN user_extend_details ued ON u.id = ued.user_id").
		Where("ue.endpoint = ? AND (ue.role = ? OR ue.role = ?) AND ue.user_type = ? AND u.status = 1",
			endpointID, "manager", "agency_manager", "user").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

// GetSalesclerkList 获取店员列表（assistant角色）
func (r *teamRepo) GetSalesclerkList(c *gin.Context, endpointID int) ([]dto.TeamMember, error) {
	var results []dto.TeamMember

	err := r.db.WithContext(c).
		Select(`u.id, u.username, u.avatar, 
				ued.name, ued.name AS extend_name, 
				ued.phone AS extend_phone, ued.wechat_id, ued.wechat_image, ued.qr_code_address,
				ue.role, u.status`).
		Table("admin_users u").
		Joins("LEFT JOIN user_endpoint ue ON u.id = ue.uid").
		Joins("LEFT JOIN user_extend_details ued ON u.id = ued.user_id").
		Where("ue.endpoint = ? AND ue.role = ? AND ue.user_type = ? AND u.status != 0",
			endpointID, "assistant", "user").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}
