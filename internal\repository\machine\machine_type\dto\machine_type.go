package dto

type MachineType struct {
	Id                int     `json:"id" `                              // id
	Name              string  `json:"name,omitempty" `                  // 型号
	NavName           string  `json:"nav_name,omitempty" `              // 实销机型名称
	ModelId           int     `json:"model_id,omitempty" `              // 机型id
	Series            int     `json:"series,omitempty" `                // 机型系列
	ModelName         string  `json:"model_name,omitempty" `            // 固件机型
	CategoryId        int     `json:"category_id,omitempty" `           // 分类id
	CategoryName      string  `json:"category_name,omitempty" gorm:"-"` // 分类名称
	CompanyPrice      float64 `json:"company_price,omitempty" `         // 公司售价
	TopAgencyPrice    float64 `json:"top_agency_price,omitempty" `      // 总代售价
	SecondAgencyPrice float64 `json:"second_agency_price,omitempty" `   // 二代售价
	CustomerPrice     float64 `json:"customer_price,omitempty" `        // 顾客售价
	ChartShow         int     `json:"chart_show,omitempty" `            // 实销统计是否展示 0:不展示 1:展示
	PrototypeStatus   int     `json:"prototype_status,omitempty" `      // 可作样机 0:不可以 1:可以
	PrototypeApkPath  string  `json:"prototype_apk_path,omitempty" `    // 演示Apk地址
	VersionCode       string  `json:"version_code,omitempty" `          // Apk版本号
	ExtBarcodeNum     int     `json:"ext_barcode_num,omitempty" `       // 副机条码数量
	Declare           int     `json:"declare,omitempty" `               // 允许申报 0:不允许 1:允许
	Stock             int     `json:"stock,omitempty" `                 // 允许备货 0:不允许 1:允许
	Visibility        int     `json:"visibility,omitempty" `            // 是否隐藏 0:隐藏 1:不隐藏
}
