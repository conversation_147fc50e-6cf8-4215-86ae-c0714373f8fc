package customer

import (
	"github.com/gin-gonic/gin"
)

type CustomerRouter interface {
	Register(r *gin.RouterGroup)
}

type customerRouter struct{}

func NewCustomerRouter() CustomerRouter {
	return &customerRouter{}
}

func (cr *customerRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	//database, _ := db.GetDB()
	//
	//// 初始化各层组件
	//customerRepository := customerRepo.NewCustomer(database)
	//customerService := customerSvc.NewCustomerService(customerRepository)
	//customerHandler := customer.NewCustomerHandler(customerService)
	//
}
