package tablet_control

import (
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/tablet_control/dto"
	"marketing-app/internal/service/tablet_control"
	"marketing-app/internal/service/tablet_control/entity"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TabletControl interface {
	AllowUnknownApp(c *gin.Context)
	GetAllowNsfwUrl(c *gin.Context)
	SetAllowNsfwUrl(c *gin.Context)
	CancelBindings(c *gin.Context)
	UnbindWatch(c *gin.Context)
}

type tabletControlHandler struct {
	service tablet_control.TabletControlSvc
}

func NewTabletControlHandler(service tablet_control.TabletControlSvc) TabletControl {
	return &tabletControlHandler{
		service: service,
	}
}

// AllowUnknownApp 设置允许未知应用安装状态（POST）或验证手机验证码（GET）
func (h *tabletControlHandler) AllowUnknownApp(c *gin.Context) {
	if c.Request.Method == "POST" {
		h.allowUnknownAppPost(c)
	} else if c.Request.Method == "GET" {
		h.allowUnknownAppGet(c)
	} else {
		handler.ResponseError(c, errors.New("不支持的请求方法"))
	}
}

// allowUnknownAppPost 设置允许未知应用安装状态
func (h *tabletControlHandler) allowUnknownAppPost(c *gin.Context) {
	var (
		req  dto.AllowUnknownAppRequest
		err  error
		resp *dto.AllowUnknownAppResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 转换为service层实体
	serviceReq := &entity.AllowUnknownAppRequest{
		Barcode:    req.Barcode,
		Number:     req.Number,
		IMEI:       req.IMEI,
		EndpointID: req.EndpointID,
		Status:     req.Status,
		UID:        uid,
	}

	// 调用服务层
	if err = h.service.AllowUnknownApp(c, serviceReq); err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.AllowUnknownAppResponse{
		Success: true,
		Message: "操作成功",
	}
}

// allowUnknownAppGet 验证手机验证码
func (h *tabletControlHandler) allowUnknownAppGet(c *gin.Context) {
	var (
		req  dto.VerifyPhoneCodeForAllowUnknownAppRequest
		err  error
		resp *dto.AllowUnknownAppResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.VerifyPhoneCodeForAllowUnknownAppRequest{
		Phone: req.Phone,
		Code:  req.Code,
	}

	// 调用服务层验证手机验证码
	if err = h.service.VerifyPhoneCodeForAllowUnknownApp(c, serviceReq); err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.AllowUnknownAppResponse{
		Success: true,
		Message: "验证成功",
	}
}

// GetAllowNsfwUrl 获取设备NSFW URL允许状态
func (h *tabletControlHandler) GetAllowNsfwUrl(c *gin.Context) {
	var (
		req  dto.GetAllowNsfwUrlRequest
		err  error
		resp *dto.GetAllowNsfwUrlResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.GetAllowNsfwUrlRequest{
		Barcode: req.Barcode,
		Number:  req.Number,
		IMEI:    req.IMEI,
	}

	// 调用服务层获取NSFW URL状态
	data, err := h.service.GetAllowNsfwUrl(c, serviceReq)
	if err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.GetAllowNsfwUrlResponse{
		Data: data,
	}
}

// SetAllowNsfwUrl 设置设备NSFW URL允许状态
func (h *tabletControlHandler) SetAllowNsfwUrl(c *gin.Context) {
	var (
		req  dto.SetAllowNsfwUrlRequest
		err  error
		resp *dto.AllowUnknownAppResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.SetAllowNsfwUrlRequest{
		Barcode:    req.Barcode,
		Number:     req.Number,
		IMEI:       req.IMEI,
		EndpointID: req.EndpointID,
		Status:     req.Status,
	}

	// 调用服务层设置NSFW URL状态
	if err = h.service.SetAllowNsfwUrl(c, serviceReq); err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.AllowUnknownAppResponse{
		Success: true,
		Message: "操作成功",
	}
}

// CancelBindings 取消设备绑定
func (h *tabletControlHandler) CancelBindings(c *gin.Context) {
	var (
		req  dto.CancelBindingsRequest
		err  error
		resp *dto.CancelBindingsResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定GET请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.CancelBindingsRequest{
		Barcode: req.Barcode,
		Number:  req.Number,
		IMEI:    req.IMEI,
	}

	// 调用服务层取消绑定
	if err = h.service.CancelBindings(c, serviceReq); err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.CancelBindingsResponse{
		Success: true,
		Message: "操作成功",
	}
}

// UnbindWatch 解绑手表
func (h *tabletControlHandler) UnbindWatch(c *gin.Context) {
	var (
		req  dto.UnbindWatchRequest
		err  error
		resp *dto.UnbindWatchResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定POST请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.UnbindWatchRequest{
		Barcode:  req.Barcode,
		Number:   req.Number,
		IMEI:     req.IMEI,
		Username: req.Username,
	}

	// 调用服务层解绑手表
	if err = h.service.UnbindWatch(c, serviceReq); err != nil {
		return
	}

	// 构造响应DTO
	resp = &dto.UnbindWatchResponse{
		Success: true,
		Message: "操作成功",
	}
}
