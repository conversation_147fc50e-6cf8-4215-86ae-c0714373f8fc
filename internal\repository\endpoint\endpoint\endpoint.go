package endpoint

import (
	"encoding/json"
	"errors"
	"marketing-app/internal/repository/warranty/base/builder"
	"math"
	"sort"
	"strings"
	"time"

	"marketing-app/internal/model"
	"marketing-app/internal/repository/endpoint/endpoint/dto"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Endpoint interface {
	GetEndpointAgency(c *gin.Context, id int) (data dto.EndpointAgency, err error)
	Activate(c *gin.Context, id int) error
	CheckPermission(c *gin.Context, id int, permission string) (bool, error)
	FindByLocation(c *gin.Context, lng, lat float64, endpointType, count int) ([]dto.EndpointItem, error)
	FindByDistrict(c *gin.Context, city, district string, endpointType int, channel string) ([]dto.EndpointItem, error)
	GetEndpoints(c *gin.Context, page int, pageSize int, endpointType int) ([]dto.EndpointItem, error)
	GetEndpointInfoByUsername(c *gin.Context, username string) (*dto.EndpointInfoByUsername, error)
	GetChannelTypes(c *gin.Context) ([]dto.ChannelType, error)
	GetEndpointImage(c *gin.Context, endpointID int) (*dto.EndpointImageInfo, []dto.CompetitorInfo, error)
	GetEndpointNewImage(c *gin.Context, endpointID int) (*dto.EndpointNewImageInfo, []dto.AuditCompetitorInfo, error)
	SaveEndpointNewImage(c *gin.Context, req map[string]interface{}) (bool, string, error)
	GetEndpointNotices(c *gin.Context, ts int64, count int) (*dto.NoticeListResponse, error)
	GetEndpointNoticeByID(c *gin.Context, noticeID string) (*dto.NoticeInfo, error)
	GetEndpointStat(c *gin.Context, topAgency, secondAgency string) ([]dto.EndpointStatItem, error)
	GetEndpointDetail(c *gin.Context, topAgency, secondAgency string, page, count int) (*dto.EndpointDetailResponse, error)
	GetEndpointRegion(c *gin.Context, roleKey map[string]interface{}, uid int) ([]dto.RegionItem, error)
}

type endpoint struct {
	db *gorm.DB
}

func NewEndpoint(db *gorm.DB) Endpoint {
	return &endpoint{
		db: db,
	}
}

func (e *endpoint) Activate(c *gin.Context, id int) (err error) {
	return e.db.WithContext(c).Model(&model.Endpoint{}).Where("id = ?", id).Update("active_at", time.Now()).Error
}

func (e *endpoint) CheckPermission(c *gin.Context, id int, permission string) (bool, error) {
	var count int64
	err := e.db.WithContext(c).
		Table("admin_permissions as ap").
		Joins("RIGHT JOIN endpoint_permissions ep ON ap.id = ep.permission_id").
		Where("ep.endpoint_id = ? AND ap.slug = ?", id, permission).
		Count(&count).Error
	return count > 0, err
}

func (e *endpoint) GetEndpointAgency(c *gin.Context, id int) (data dto.EndpointAgency, err error) {
	err = e.db.WithContext(c).
		Table("endpoint as e").
		Select("e.id, e.top_agency, e.second_agency, e.is_direct_sales, e.compound, a.channel as endpoint_channel, e.status").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Where("e.id = ?", id).
		Find(&data).Error
	return
}

// FindByLocation 根据地理位置查找终端
func (e *endpoint) FindByLocation(c *gin.Context, lng, lat float64, endpointType, count int) ([]dto.EndpointItem, error) {
	var endpoints []dto.EndpointItem

	// 计算搜索范围（10km半径）
	const R = 10.0                      // 半径（km）
	r := 180.0 / math.Pi * R / 6372.797 // 里面的 1 就代表搜索 1km 之内，单位km
	lngR := r / math.Cos(lat*3.14159265359/180.0)
	maxLat := lat + r
	minLat := lat - r
	maxLng := lng + lngR
	minLng := lng - lngR

	// 构建查询条件
	query := e.db.WithContext(c).Table("endpoint").
		Select("id, name, phone, address, city, lng, lat, blng, blat, is_pre_sale, is_after_sale").
		Where("((lat BETWEEN ? AND ?) AND (lng BETWEEN ? AND ?)) AND status = 1", minLat, maxLat, minLng, maxLng)

	// 根据类型添加条件
	switch endpointType {
	case 1: // 售前
		query = query.Where("is_pre_sale = 1")
	case 2: // 售后
		query = query.Where("is_after_sale = 1")
	}

	// 限制返回数量
	query = query.Limit(count)

	err := query.Find(&endpoints).Error
	if err != nil {
		return nil, err
	}

	// 计算距离并排序
	for i := range endpoints {
		endpoints[i].Distance = calculateDistance(lat, lng, endpoints[i].Lat, endpoints[i].Lng)
	}

	// 按距离排序
	sort.Slice(endpoints, func(i, j int) bool {
		return endpoints[i].Distance < endpoints[j].Distance
	})

	return endpoints, nil
}

// FindByDistrict 根据地区查找终端
func (e *endpoint) FindByDistrict(c *gin.Context, city, district string, endpointType int, channel string) ([]dto.EndpointItem, error) {
	var endpoints []dto.EndpointItem

	// 构建基础查询
	var query *gorm.DB
	if channel != "" {
		// 需要关联agency表查询渠道
		query = e.db.WithContext(c).Table("endpoint").
			Select("endpoint.id, endpoint.name, endpoint.phone, endpoint.address, endpoint.city, endpoint.lng, endpoint.lat, endpoint.blng, endpoint.blat, endpoint.is_pre_sale, endpoint.is_after_sale").
			Joins("LEFT JOIN agency ON endpoint.top_agency = agency.id")
	} else {
		query = e.db.WithContext(c).Table("endpoint").
			Select("id, name, phone, address, city, lng, lat, blng, blat, is_pre_sale, is_after_sale")
	}

	// 添加城市条件
	query = query.Where("city = ?", city)

	// 添加区域条件
	if district != "" {
		query = query.Where("district = ?", district)
	}

	// 添加渠道条件
	if channel != "" {
		query = query.Where("agency.channel = ?", channel)
	}

	// 添加状态条件
	query = query.Where("endpoint.status = 1")

	// 根据类型添加条件
	switch endpointType {
	case 1: // 售前
		query = query.Where("endpoint.is_pre_sale = 1")
	case 2: // 售后
		query = query.Where("endpoint.is_after_sale = 1")
	}

	err := query.Find(&endpoints).Error
	return endpoints, err
}

// GetEndpoints 获取所有终端
func (e *endpoint) GetEndpoints(c *gin.Context, page int, pageSize int, endpointType int) ([]dto.EndpointItem, error) {
	var endpoints []dto.EndpointItem

	query := e.db.WithContext(c).Table("endpoint").
		Select("id, name, phone, address, city, lng, lat, blng, blat, is_pre_sale, is_after_sale").
		Where("status = 1")

	// 根据类型添加条件
	switch endpointType {
	case 1: // 售前
		query = query.Where("is_pre_sale = 1")
	case 2: // 售后
		query = query.Where("is_after_sale = 1")
	}

	// 分页
	query = query.Scopes(builder.Paginate(page, pageSize))

	err := query.Find(&endpoints).Error
	return endpoints, err
}

// GetEndpointInfoByUsername 根据用户名获取终端信息
func (e *endpoint) GetEndpointInfoByUsername(c *gin.Context, username string) (*dto.EndpointInfoByUsername, error) {
	var result dto.EndpointInfoByUsername

	err := e.db.WithContext(c).
		Table("endpoint e").
		Select("e.id, e.name, a.name as top_agency, b.name as second_agency, e.address, e.manager, e.phone").
		Joins("JOIN user_endpoint ue ON ue.endpoint = e.id").
		Joins("JOIN admin_users u ON u.id = ue.uid").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Joins("LEFT JOIN agency b ON e.second_agency = b.id").
		Where("u.username = ?", username).
		First(&result).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 返回nil表示未找到数据，由上层处理错误消息
		}
		return nil, err
	}

	return &result, nil
}

// GetChannelTypes 获取终端渠道类型
func (e *endpoint) GetChannelTypes(c *gin.Context) ([]dto.ChannelType, error) {
	var channelTypes []dto.ChannelType

	err := e.db.WithContext(c).
		Table("endpoint_channel").
		Select("id, name").
		Find(&channelTypes).Error

	return channelTypes, err
}

// GetEndpointImage 获取终端形象信息
func (e *endpoint) GetEndpointImage(c *gin.Context, endpointID int) (*dto.EndpointImageInfo, []dto.CompetitorInfo, error) {
	// 查询主要终端信息
	var endpointInfo dto.EndpointImageInfo
	err := e.db.WithContext(c).
		Table("endpoint ep").
		Select("ep.name, ep.manager, ep.phone, ep.images, ep.lng, ep.lat, ep.code, ext.endpoint, "+
			"ext.channel_level, ext.channel_type, ext.position_priority, ext.area, ext.sales, "+
			"DATE_FORMAT(ep.created_at, '%Y-%m-%d %H:%i:%s') as created_at").
		Joins("LEFT JOIN endpoint_extend ext ON ep.id = ext.endpoint").
		Where("ep.id = ?", endpointID).
		First(&endpointInfo).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil, nil // 返回nil表示未找到数据
		}
		return nil, nil, err
	}

	// 查询竞争品牌信息
	var competitors []dto.CompetitorInfo
	err = e.db.WithContext(c).
		Table("endpoint_compete_brand").
		Select("id, brand, position_priority, pics").
		Where("endpoint = ?", endpointID).
		Find(&competitors).Error

	if err != nil {
		return nil, nil, err
	}

	return &endpointInfo, competitors, nil
}

// GetEndpointNewImage 获取终端形象变更信息
func (e *endpoint) GetEndpointNewImage(c *gin.Context, endpointID int) (*dto.EndpointNewImageInfo, []dto.AuditCompetitorInfo, error) {
	// 查询终端形象变更信息
	var newImageInfo dto.EndpointNewImageInfo
	err := e.db.WithContext(c).
		Table("endpoint_audit ext").
		Select("ep.name, ep.manager, ep.phone, ext.*,"+
			"CASE WHEN ext.created_at IS NOT NULL THEN DATE_FORMAT(ext.created_at, '%Y-%m-%d %H:%i:%s') END as created_at,"+
			"CASE WHEN ext.updated_at IS NOT NULL THEN DATE_FORMAT(ext.updated_at, '%Y-%m-%d %H:%i:%s') END as updated_at,"+
			"CASE WHEN ext.top_agency_audited_at IS NOT NULL THEN DATE_FORMAT(ext.top_agency_audited_at, '%Y-%m-%d %H:%i:%s') END as top_agency_audited_at,"+
			"CASE WHEN ext.manager_audited_at IS NOT NULL THEN DATE_FORMAT(ext.manager_audited_at, '%Y-%m-%d %H:%i:%s') END as manager_audited_at").
		Joins("LEFT JOIN endpoint ep ON ep.id = ext.endpoint").
		Where("ext.endpoint = ? AND ext.status != 2", endpointID).
		First(&newImageInfo).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, nil // 返回nil表示未找到数据
		}
		return nil, nil, err
	}

	// 查询审核竞争品牌信息
	var competitors []dto.AuditCompetitorInfo
	err = e.db.WithContext(c).
		Table("endpoint_audit_compete_brand").
		Select("brand, position_priority, pics").
		Where("audit_id = ?", newImageInfo.ID).
		Find(&competitors).Error

	if err != nil {
		return nil, nil, err
	}

	return &newImageInfo, competitors, nil
}

// SaveEndpointNewImage 保存终端形象变更
func (e *endpoint) SaveEndpointNewImage(c *gin.Context, req map[string]interface{}) (bool, string, error) {
	// 开启事务
	tx := e.db.WithContext(c).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 保存/更新主表数据
	auditID, err := e.saveExtend(tx, req)
	if err != nil {
		tx.Rollback()
		return false, err.Error(), nil
	}

	// 保存竞争品牌数据
	err = e.saveCompetitors(tx, req, auditID)
	if err != nil {
		tx.Rollback()
		return false, err.Error(), nil
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return false, "事务提交失败: " + err.Error(), nil
	}

	return true, "success", nil
}

// saveExtend 保存/更新endpoint_audit主表数据
func (e *endpoint) saveExtend(tx *gorm.DB, info map[string]interface{}) (int, error) {
	id, hasID := info["id"]
	endpointId, hasEndpoint := info["endpoint"]
	// 定义可处理的字段
	fields := []string{"endpoint", "lng", "lat", "position_priority", "area", "sales", "channel_level", "channel_type", "images"}

	if hasID && id != nil {
		// 更新现有申请
		auditID := id.(int)

		// 构建更新数据
		updateData := make(map[string]interface{})
		for _, field := range fields {
			if value, exists := info[field]; exists {
				if field == "images" {
					// 处理图片字段
					if imageList, ok := value.([]interface{}); ok {
						var imageURLs []string
						for _, img := range imageList {
							if imgStr, ok := img.(string); ok {
								imageURLs = append(imageURLs, imgStr)
							}
						}
						jsonStr, err := e.dumpImages(imageURLs)
						if err != nil {
							return 0, err
						}
						updateData[field] = jsonStr
					}
				} else {
					updateData[field] = value
				}
			}
		}

		// 添加固定更新字段
		updateData["updated_at"] = time.Now()
		updateData["status"] = 0
		updateData["top_agency_opinion"] = nil
		updateData["top_agency_audited_at"] = nil
		updateData["manager_opinion"] = nil
		updateData["manager_audited_at"] = nil

		// 执行更新
		result := tx.Model(&model.EndpointAudit{}).
			Where("id = ? AND status <= 0", auditID).
			Updates(updateData)

		if result.Error != nil {
			return 0, result.Error
		}
		if result.RowsAffected == 0 {
			return 0, errors.New("形象变更申请不存在或正在审批中，无法修改")
		}

		return auditID, nil

	} else if hasEndpoint && endpointId != nil {
		// 创建新申请
		endpointID := endpointId.(int)
		// 检查是否已有审核中的申请
		var count int64
		tx.Model(&model.EndpointAudit{}).
			Where("endpoint = ? AND status != 2", endpointID).
			Count(&count)

		if count > 0 {
			return 0, errors.New("形象变更申请正在等待审批，无法创建新的申请")
		}
		// 构建新申请数据
		auditData := &model.EndpointAudit{
			Endpoint: endpointID,
		}

		// 填充字段数据
		for _, field := range fields {
			if value, exists := info[field]; exists && value != nil {
				switch field {
				case "lng":
					if str, ok := value.(string); ok {
						auditData.Lng = str
					}
				case "lat":
					if str, ok := value.(string); ok {
						auditData.Lat = str
					}
				case "position_priority":
					if str, ok := value.(string); ok {
						auditData.PositionPriority = &str
					}
				case "area":
					if num, ok := value.(float64); ok {
						intVal := int(num)
						auditData.Area = &intVal
					}
				case "sales":
					if num, ok := value.(float64); ok {
						intVal := int(num)
						auditData.Sales = &intVal
					}
				case "channel_level":
					if num, ok := value.(int); ok {
						intVal := int8(num)
						auditData.ChannelLevel = &intVal
					}
				case "channel_type":
					if num, ok := value.(int); ok {
						intVal := int8(num)
						auditData.ChannelType = &intVal
					}
				case "images":
					if imageList, ok := value.([]interface{}); ok {
						var imageURLs []string
						for _, img := range imageList {
							if imgStr, ok := img.(string); ok {
								imageURLs = append(imageURLs, imgStr)
							}
						}
						jsonStr, err := e.dumpImages(imageURLs)
						if err != nil {
							return 0, err
						}
						auditData.Images = &jsonStr
					}
				}
			}
		}

		// 插入新记录
		if err := tx.Create(auditData).Error; err != nil {
			return 0, err
		}

		return auditData.ID, nil

	} else {
		return 0, errors.New("缺少参数【变更申请编号】或【终端编号】")
	}
}

// saveCompetitors 保存竞争品牌数据
func (e *endpoint) saveCompetitors(tx *gorm.DB, info map[string]interface{}, auditID int) error {
	competitors, hasCompetitors := info["competitors"]
	if !hasCompetitors {
		return nil
	}

	// 如果是更新操作，先删除现有竞争品牌
	if _, hasID := info["id"]; hasID {
		if err := tx.Where("audit_id = ?", auditID).Delete(&model.EndpointAuditCompeteBrand{}).Error; err != nil {
			return err
		}
	}

	// 处理竞争品牌数据
	if competitorList, ok := competitors.([]interface{}); ok && len(competitorList) > 0 {
		var competitorData []model.EndpointAuditCompeteBrand

		// 最多处理5个竞争品牌
		limit := len(competitorList)
		if limit > 5 {
			limit = 5
		}

		for i := 0; i < limit; i++ {
			comp := competitorList[i]
			if compMap, ok := comp.(map[string]interface{}); ok {
				competitor := model.EndpointAuditCompeteBrand{
					AuditID: auditID,
					Brand:   compMap["brand"].(string),
				}

				// 处理position_priority
				if pp, exists := compMap["position_priority"]; exists && pp != nil {
					if ppStr, ok := pp.(string); ok {
						competitor.PositionPriority = &ppStr
					}
				}

				// 处理pics
				if pics, exists := compMap["pics"]; exists && pics != nil {
					if picsList, ok := pics.([]interface{}); ok {
						var picURLs []string
						for _, pic := range picsList {
							if picStr, ok := pic.(string); ok {
								picURLs = append(picURLs, picStr)
							}
						}
						jsonStr, err := e.dumpImages(picURLs)
						if err != nil {
							return err
						}
						competitor.Pics = &jsonStr
					}
				}

				competitorData = append(competitorData, competitor)
			}
		}

		// 批量插入竞争品牌
		if len(competitorData) > 0 {
			if err := tx.Create(&competitorData).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// dumpImages 将图片URL数组转换为JSON字符串（去除域名前缀）
func (e *endpoint) dumpImages(imageURLs []string) (string, error) {
	const IMG_HOST = "https://dt.readboy.com/"
	var imageFiles []string

	for _, url := range imageURLs {
		if url != "" {
			if !strings.HasPrefix(url, IMG_HOST) {
				return "", errors.New("invalid image url=" + url)
			}
			imagePath := strings.TrimPrefix(url, IMG_HOST)
			imageFiles = append(imageFiles, imagePath)
		}
	}

	jsonBytes, err := json.Marshal(imageFiles)
	if err != nil {
		return "", err
	}

	return string(jsonBytes), nil
}

// calculateDistance 计算两点间距离（km）
func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const R = 6371.0 // 地球半径（km）

	lat1Rad := lat1 * math.Pi / 180.0
	lat2Rad := lat2 * math.Pi / 180.0
	deltaLat := (lat2 - lat1) * math.Pi / 180.0
	deltaLng := (lng2 - lng1) * math.Pi / 180.0

	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLng/2)*math.Sin(deltaLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return R * c
}

// GetEndpointNotices 获取终端公告列表
func (e *endpoint) GetEndpointNotices(c *gin.Context, ts int64, count int) (*dto.NoticeListResponse, error) {
	// 如果ts <= 0，则设为0
	if ts <= 0 {
		ts = 0
	}

	// 将时间戳转换为时间字符串
	var startTime time.Time
	if ts > 0 {
		startTime = time.Unix(ts, 0)
	}

	// 查询count+1条记录用于判断是否还有更多数据
	var notices []model.EndpointNotice
	query := e.db.WithContext(c).Model(&model.EndpointNotice{})

	// 如果提供了时间戳，则添加时间过滤条件
	if ts > 0 {
		query = query.Where("created_at > ?", startTime)
	}

	// 按创建时间倒序排列，限制数量为count+1
	if err := query.Order("created_at DESC").Limit(count + 1).Find(&notices).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回nil
	if len(notices) == 0 {
		return nil, nil
	}

	// 判断是否已经到最后一页
	isEnd := len(notices) < count+1
	size := len(notices)
	if !isEnd {
		size = count
	}

	// 获取实际返回的数据（最多count条）
	actualData := notices[:size]

	// 转换为DTO格式
	data := make([]dto.NoticeInfo, len(actualData))
	for i, notice := range actualData {
		createdAt := ""
		if notice.CreatedAt != nil {
			createdAt = notice.CreatedAt.Format("2006.01.02 15:04:05")
		}

		data[i] = dto.NoticeInfo{
			ID:        int(notice.ID),
			Content:   notice.Content,
			Title:     notice.Title,
			Author:    notice.Author,
			CreatedAt: createdAt,
		}
	}

	return &dto.NoticeListResponse{
		Data:  data,
		Size:  size,
		IsEnd: isEnd,
	}, nil
}

// GetEndpointNoticeByID 根据ID获取终端公告
func (e *endpoint) GetEndpointNoticeByID(c *gin.Context, noticeID string) (*dto.NoticeInfo, error) {
	var notice model.EndpointNotice

	// 根据ID查询单条记录
	if err := e.db.WithContext(c).Where("id = ?", noticeID).First(&notice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没找到数据返回nil
		}
		return nil, err
	}

	// 转换为DTO格式
	createdAt := ""
	if notice.CreatedAt != nil {
		createdAt = notice.CreatedAt.Format("2006.01.02 15:04:05")
	}

	return &dto.NoticeInfo{
		ID:        int(notice.ID),
		Content:   notice.Content,
		Title:     notice.Title,
		Author:    notice.Author,
		CreatedAt: createdAt,
	}, nil
}

// GetEndpointStat 获取终端统计信息
func (e *endpoint) GetEndpointStat(c *gin.Context, topAgency, secondAgency string) ([]dto.EndpointStatItem, error) {
	var result []dto.EndpointStatItem

	// 构建基础查询
	query := e.db.WithContext(c).Model(&model.Endpoint{}).
		Select("type, COUNT(*) as total").
		Where("status = ?", 1).                 // 启用状态
		Where("type != ? AND type != ?", 0, 2). // 排除type=0和type=2
		Group("type").
		Order("CASE WHEN type=3 THEN 1 WHEN type=4 THEN 2 WHEN type=1 THEN 3 END") // 自定义排序: 3->4->1

	// 根据参数添加条件
	if topAgency != "" && secondAgency != "" {
		// 两个代理参数都有
		query = query.Where("top_agency = ? AND second_agency = ?", topAgency, secondAgency)
	} else if topAgency != "" {
		// 只有一级代理
		query = query.Where("top_agency = ?", topAgency)
	} else if secondAgency != "" {
		// 只有二级代理
		query = query.Where("second_agency = ?", secondAgency)
	}
	// 如果两个参数都为空，则查询所有数据（不添加额外条件）

	// 执行查询
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

// GetEndpointDetail 获取终端详情信息
func (e *endpoint) GetEndpointDetail(c *gin.Context, topAgency, secondAgency string, page, count int) (*dto.EndpointDetailResponse, error) {
	// 限制count最大为50
	if count > 50 {
		count = 50
	}

	// 计算offset
	offset := (page - 1) * count
	if offset < 0 {
		offset = 0
	}

	var items []dto.EndpointDetailItem

	// 构建基础查询
	query := e.db.WithContext(c).Model(&model.Endpoint{}).
		Select("name, type, top_agency, second_agency").
		Where("status = ?", 1).                 // 启用状态
		Where("type != ? AND type != ?", 0, 2). // 排除type=0和type=2
		Offset(offset).
		Limit(count + 1) // 查询count+1条记录用于判断是否还有更多数据

	// 根据参数添加条件
	if topAgency != "" && secondAgency != "" {
		// 两个代理参数都有
		query = query.Where("top_agency = ? AND second_agency = ?", topAgency, secondAgency)
	} else if topAgency != "" {
		// 只有一级代理
		query = query.Where("top_agency = ?", topAgency)
	} else if secondAgency != "" {
		// 只有二级代理
		query = query.Where("second_agency = ?", secondAgency)
	}
	// 如果两个参数都为空，则查询所有数据（不添加额外条件）

	// 执行查询
	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	// 如果没有数据，返回nil
	if len(items) == 0 {
		return nil, nil
	}

	// 判断是否已经到最后一页
	isEnd := len(items) < count+1
	size := len(items)
	if !isEnd {
		size = count
	}

	// 获取实际返回的数据（最多count条）
	actualData := items[:size]

	return &dto.EndpointDetailResponse{
		Detail: actualData,
		IsEnd:  isEnd,
	}, nil
}

// GetEndpointRegion 获取终端区域信息
func (e *endpoint) GetEndpointRegion(c *gin.Context, roleKey map[string]interface{}, uid int) ([]dto.RegionItem, error) {
	var regions []dto.RegionItem

	// 获取角色信息
	roleSlug := ""
	if slug, exists := roleKey["slug"]; exists {
		if slugStr, ok := slug.(string); ok {
			roleSlug = slugStr
		}
	}

	// 根据角色类型执行不同的查询
	if strings.Contains(roleSlug, "leader") {
		// 领导角色：返回所有代理区域数据
		err := e.db.WithContext(c).
			Table("agency").
			Select("id, pid, name, level").
			Where("deleted_at IS NULL").
			Group("level, name").
			Find(&regions).Error

		if err != nil {
			return nil, err
		}

	} else if strings.Contains(roleSlug, "topAgency") {
		// 一级代理角色：返回与该用户相关的一级代理及其下级区域数据
		// 使用GORM分别执行两个查询，然后合并结果

		// 查询1: 用户的一级代理
		var regions1 []dto.RegionItem
		err := e.db.WithContext(c).
			Table("agency a").
			Select("a.id, a.pid, a.name, a.level").
			Joins("RIGHT JOIN user_agency ua ON ua.top_agency = a.id").
			Where("ua.uid = ? AND a.deleted_at IS NULL", uid).
			Group("a.level, a.name").
			Find(&regions1).Error

		if err != nil {
			return nil, err
		}

		// 查询2: 用户一级代理的下级区域
		var regions2 []dto.RegionItem
		err = e.db.WithContext(c).
			Table("agency a").
			Select("a.id, a.pid, a.name, a.level").
			Joins("RIGHT JOIN user_agency ua ON ua.top_agency = a.pid").
			Where("ua.uid = ? AND a.deleted_at IS NULL", uid).
			Group("a.level, a.name").
			Find(&regions2).Error

		if err != nil {
			return nil, err
		}

		// 合并结果并去重
		regionMap := make(map[int]dto.RegionItem)
		for _, region := range regions1 {
			regionMap[region.ID] = region
		}
		for _, region := range regions2 {
			regionMap[region.ID] = region
		}

		// 转换为切片
		for _, region := range regionMap {
			regions = append(regions, region)
		}

	} else {
		// 其他角色（二级代理等）：返回与该用户相关的二级代理及相关区域数据
		// 使用GORM分别执行两个查询，然后合并结果

		// 查询1: 用户的二级代理
		var regions1 []dto.RegionItem
		err := e.db.WithContext(c).
			Table("agency a").
			Select("a.id, a.pid, a.name, a.level").
			Joins("RIGHT JOIN user_agency ua ON ua.second_agency = a.id").
			Where("ua.uid = ? AND a.deleted_at IS NULL", uid).
			Group("a.level, a.name").
			Find(&regions1).Error

		if err != nil {
			return nil, err
		}

		// 查询2: 用户相关的一级代理
		var regions2 []dto.RegionItem
		err = e.db.WithContext(c).
			Table("agency a").
			Select("a.id, a.pid, a.name, a.level").
			Joins("RIGHT JOIN user_agency ua ON ua.top_agency = a.id").
			Where("ua.uid = ? AND a.deleted_at IS NULL", uid).
			Group("a.level, a.name").
			Find(&regions2).Error

		if err != nil {
			return nil, err
		}

		// 合并结果并去重
		regionMap := make(map[int]dto.RegionItem)
		for _, region := range regions1 {
			regionMap[region.ID] = region
		}
		for _, region := range regions2 {
			regionMap[region.ID] = region
		}

		// 转换为切片
		for _, region := range regionMap {
			regions = append(regions, region)
		}
	}

	return regions, nil
}
