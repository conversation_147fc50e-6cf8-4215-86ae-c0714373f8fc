package model

import (
	"time"
)

type TrainCollection struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	TrainID   uint      `gorm:"type:int(10) unsigned;not null;comment:资料id" json:"train_id"`
	UserID    uint      `gorm:"type:int(10) unsigned;not null;comment:用户id" json:"user_id"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00'" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00'" json:"updated_at"`
}

func (TrainCollection) TableName() string {
	return "train_collection"
}
