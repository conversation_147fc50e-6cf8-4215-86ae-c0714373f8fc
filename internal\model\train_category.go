package model

import (
	"time"
)

type TrainCategory struct {
	ID        uint       `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	Title     string     `gorm:"type:varchar(50);not null;default:'';comment:名称" json:"title"`
	ParentID  uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:父类id" json:"parent_id"`
	Order     uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:排序" json:"order"`
	CreatedAt *time.Time `gorm:"type:timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
}

func (TrainCategory) TableName() string {
	return "train_category"
}
