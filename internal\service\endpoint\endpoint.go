package endpoint

import (
	"go.uber.org/zap"
	"marketing-app/internal/api/client/salesstat"
	"marketing-app/internal/handler/endpoint/dto"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/repository"
	"marketing-app/internal/repository/endpoint/endpoint"
	repoDto "marketing-app/internal/repository/endpoint/endpoint/dto"
	"marketing-app/internal/service/endpoint/entity"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type EndpointSvc interface {
	GetEndpoints(c *gin.Context, req *entity.Endpoints) (*repoDto.EndpointsResponse, error)
	GetEndpointOptions(c *gin.Context) (*dto.EndpointOptionsResponse, error)
	GetEndpointImage(c *gin.Context, endpointID int) (*dto.EndpointImageResponse, error)
	GetEndpointNewImage(c *gin.Context, endpointID int) (*dto.EndpointNewImageResponse, error)
	SaveEndpointNewImage(c *gin.Context, req *dto.EndpointNewImagePostRequest) (bool, string)
	GetEndpointNotice(c *gin.Context, ts, count int) (*dto.EndpointNoticeResponse, error)
	GetEndpointNoticeById(c *gin.Context, noticeID string) (*dto.NoticeItem, error)
	GetEndpointInfoByUsername(c *gin.Context, username string) (*dto.EndpointInfoByUsernameResponse, error)
	GetEndpointRegion(c *gin.Context, uid int) (*dto.EndpointRegionResponse, error)
	GetEndpointStat(c *gin.Context, topAgency, secondAgency string) (*dto.EndpointStatResponse, error)
	GetEndpointDetail(c *gin.Context, uid int, topAgency, secondAgency string, page, count int) (*dto.EndpointDetailResponse, error)
	GetSalesStat(c *gin.Context, accessToken string) (*dto.SalesStatResponse, error)
}

type endpointSvc struct {
	endpointRepo    endpoint.Endpoint
	adminUserRepo   repository.AdminUserRepository
	salesStatClient salesstat.SalesStatClient
}

func NewEndpointService(endpointRepo endpoint.Endpoint, adminUserRepo repository.AdminUserRepository, salesStatClient salesstat.SalesStatClient) EndpointSvc {
	return &endpointSvc{
		endpointRepo:    endpointRepo,
		adminUserRepo:   adminUserRepo,
		salesStatClient: salesStatClient,
	}
}

// GetEndpoints 获取终端列表
func (e *endpointSvc) GetEndpoints(c *gin.Context, req *entity.Endpoints) (*repoDto.EndpointsResponse, error) {
	var repoEndpoints []repoDto.EndpointItem
	var err error

	// 根据查询条件选择不同的查询方式
	if req.Lng > 0 && req.Lat > 0 {
		// 按地理位置查询
		repoEndpoints, err = e.endpointRepo.FindByLocation(c, req.Lng, req.Lat, req.Type, req.Count)
		if err != nil {
			return nil, errors.Wrap(err, "按地理位置查询终端失败")
		}
	} else if req.City != "" {
		// 按地区查询
		repoEndpoints, err = e.endpointRepo.FindByDistrict(c, req.City, req.District, req.Type, req.Channel)
		if err != nil {
			return nil, errors.Wrap(err, "按地区查询终端失败")
		}
	} else {
		// 获取所有终端
		repoEndpoints, err = e.endpointRepo.GetEndpoints(c, req.Page, req.Count, req.Type)
		if err != nil {
			return nil, errors.Wrap(err, "获取终端列表失败")
		}
	}

	return &repoDto.EndpointsResponse{
		Data: repoEndpoints,
	}, nil
}

// GetEndpointOptions 获取终端选项
func (e *endpointSvc) GetEndpointOptions(c *gin.Context) (*dto.EndpointOptionsResponse, error) {
	// 从数据库获取渠道类型
	channelTypesRepo, err := e.endpointRepo.GetChannelTypes(c)
	if err != nil {
		return nil, errors.Wrap(err, "获取渠道类型失败")
	}

	// 转换为handler层DTO格式
	channelTypes := make([]dto.OptionItem, len(channelTypesRepo))
	for i, ct := range channelTypesRepo {
		channelTypes[i] = dto.OptionItem{
			ID:   ct.ID,
			Name: ct.Name,
		}
	}

	return &dto.EndpointOptionsResponse{
		ChannelLevel: []dto.OptionItem{
			{ID: 1, Name: "省级"},
			{ID: 2, Name: "市级"},
			{ID: 3, Name: "县级"},
		},
		PositionPriority: []string{"1号位", "2号位", "3号位", "4号位"},
		CompetitorBrand:  []string{"步步高", "好记星", "优学派", "快易典"},
		ChannelType:      channelTypes, // 从数据库读取
	}, nil
}

// GetEndpointImage 获取终端形象
func (e *endpointSvc) GetEndpointImage(c *gin.Context, endpointID int) (*dto.EndpointImageResponse, error) {
	// 调用repository层获取终端形象信息
	endpointInfo, competitors, err := e.endpointRepo.GetEndpointImage(c, endpointID)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端形象信息失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if endpointInfo == nil {
		return nil, nil
	}

	// 处理图片URL
	images := loadImages(endpointInfo.Images)

	// 转换竞争品牌信息
	competitorItems := make([]dto.Competitor, len(competitors))
	for i, comp := range competitors {
		pics := loadImages(comp.Pics)
		positionPriority := ""
		if comp.PositionPriority != nil {
			positionPriority = *comp.PositionPriority
		}

		competitorItems[i] = dto.Competitor{
			ID:               comp.ID,
			Brand:            comp.Brand,
			PositionPriority: positionPriority,
			Pics:             pics,
		}
	}

	// 构建响应数据
	response := &dto.EndpointImageResponse{
		Name:        endpointInfo.Name,
		Manager:     "",
		Phone:       "",
		Images:      images,
		Lng:         0,
		Lat:         0,
		Code:        endpointInfo.Code,
		Endpoint:    endpointID,
		CreatedAt:   endpointInfo.CreatedAt,
		Competitors: competitorItems,
	}

	// 处理可能为空的字段
	if endpointInfo.Manager != nil {
		response.Manager = *endpointInfo.Manager
	}
	if endpointInfo.Phone != nil {
		response.Phone = *endpointInfo.Phone
	}
	if endpointInfo.Endpoint != nil {
		response.Endpoint = *endpointInfo.Endpoint
	}
	if endpointInfo.ChannelLevel != nil {
		response.ChannelLevel = int(*endpointInfo.ChannelLevel)
	}
	if endpointInfo.ChannelType != nil {
		response.ChannelType = int(*endpointInfo.ChannelType)
	}
	if endpointInfo.PositionPriority != nil {
		response.PositionPriority = *endpointInfo.PositionPriority
	}
	if endpointInfo.Area != nil {
		response.Area = strconv.Itoa(*endpointInfo.Area) // 将int转换为string
	}
	if endpointInfo.Sales != nil {
		response.Sales = strconv.Itoa(*endpointInfo.Sales) // 将int转换为string
	}

	// 处理经纬度（从string转换为float64）
	if lng, err := strconv.ParseFloat(endpointInfo.Lng, 64); err == nil {
		response.Lng = lng
	}
	if lat, err := strconv.ParseFloat(endpointInfo.Lat, 64); err == nil {
		response.Lat = lat
	}

	return response, nil
}

// GetEndpointNewImage 获取终端形象变更
func (e *endpointSvc) GetEndpointNewImage(c *gin.Context, endpointID int) (*dto.EndpointNewImageResponse, error) {
	// 调用repository层获取终端形象变更信息
	newImageInfo, competitors, err := e.endpointRepo.GetEndpointNewImage(c, endpointID)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端形象变更信息失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if newImageInfo == nil {
		return nil, nil
	}

	// 处理图片URL
	images := loadImages(newImageInfo.Images)

	// 转换竞争品牌信息
	competitorItems := make([]dto.Competitor, len(competitors))
	for i, comp := range competitors {
		pics := loadImages(comp.Pics)
		positionPriority := ""
		if comp.PositionPriority != nil {
			positionPriority = *comp.PositionPriority
		}

		competitorItems[i] = dto.Competitor{
			ID:               0, // 审核表中的竞争品牌没有ID字段
			Brand:            comp.Brand,
			PositionPriority: positionPriority,
			Pics:             pics,
		}
	}

	// 构建响应数据
	response := &dto.EndpointNewImageResponse{
		ID:          newImageInfo.ID,
		Name:        newImageInfo.Name,
		Manager:     "",
		Phone:       "",
		Images:      images,
		Lng:         0,
		Lat:         0,
		Competitors: competitorItems,
	}

	// 处理可能为空的字段
	if newImageInfo.Manager != nil {
		response.Manager = *newImageInfo.Manager
	}
	if newImageInfo.Phone != nil {
		response.Phone = *newImageInfo.Phone
	}
	if newImageInfo.PositionPriority != nil {
		response.PositionPriority = *newImageInfo.PositionPriority
	}
	if newImageInfo.Area != nil {
		response.Area = strconv.Itoa(*newImageInfo.Area)
	}
	if newImageInfo.Sales != nil {
		response.Sales = strconv.Itoa(*newImageInfo.Sales)
	}
	if newImageInfo.ChannelLevel != nil {
		response.ChannelLevel = int(*newImageInfo.ChannelLevel)
	}
	if newImageInfo.ChannelType != nil {
		response.ChannelType = int(*newImageInfo.ChannelType)
	}

	// 处理状态和审核信息
	response.Status = int(newImageInfo.Status)
	if newImageInfo.TopAgencyOpinion != nil {
		response.TopAgencyOpinion = newImageInfo.TopAgencyOpinion
	}
	if newImageInfo.ManagerOpinion != nil {
		response.ManagerOpinion = newImageInfo.ManagerOpinion
	}
	if newImageInfo.CreatedAt != nil {
		response.CreatedAt = newImageInfo.CreatedAt
	}
	if newImageInfo.UpdatedAt != nil {
		response.UpdatedAt = newImageInfo.UpdatedAt
	}
	if newImageInfo.TopAgencyAuditedAt != nil {
		response.TopAgencyAuditedAt = newImageInfo.TopAgencyAuditedAt
	}
	if newImageInfo.ManagerAuditedAt != nil {
		response.ManagerAuditedAt = newImageInfo.ManagerAuditedAt
	}

	// 处理经纬度（从string转换为float64）
	if lng, err := strconv.ParseFloat(newImageInfo.Lng, 64); err == nil {
		response.Lng = lng
	}
	if lat, err := strconv.ParseFloat(newImageInfo.Lat, 64); err == nil {
		response.Lat = lat
	}

	return response, nil
}

// SaveEndpointNewImage 保存终端形象变更
func (e *endpointSvc) SaveEndpointNewImage(c *gin.Context, req *dto.EndpointNewImagePostRequest) (bool, string) {
	// 将DTO转换为map[string]interface{}格式，匹配Python版本的数据结构
	reqMap := make(map[string]interface{})

	// 转换基本字段
	if req.ID != nil {
		reqMap["id"] = *req.ID
	}
	if req.Endpoint != nil {
		reqMap["endpoint"] = *req.Endpoint
	}

	reqMap["lng"] = req.Lng
	reqMap["lat"] = req.Lat
	reqMap["position_priority"] = req.PositionPriority
	reqMap["area"] = float64(req.Area)   // 转换为float64以匹配JSON解析
	reqMap["sales"] = float64(req.Sales) // 转换为float64以匹配JSON解析
	reqMap["channel_level"] = req.ChannelLevel
	reqMap["channel_type"] = req.ChannelType

	// 转换图片数组
	if len(req.Images) > 0 {
		images := make([]interface{}, len(req.Images))
		for i, img := range req.Images {
			images[i] = img
		}
		reqMap["images"] = images
	}

	// 转换竞争品牌数组
	if len(req.Competitors) > 0 {
		competitors := make([]interface{}, len(req.Competitors))
		for i, comp := range req.Competitors {
			compMap := map[string]interface{}{
				"brand":             comp.Brand,
				"position_priority": comp.PositionPriority,
			}

			// 转换竞争品牌图片
			if len(comp.Pics) > 0 {
				pics := make([]interface{}, len(comp.Pics))
				for j, pic := range comp.Pics {
					pics[j] = pic
				}
				compMap["pics"] = pics
			}

			competitors[i] = compMap
		}
		reqMap["competitors"] = competitors
	}
	// 调用repository层保存数据
	success, message, err := e.endpointRepo.SaveEndpointNewImage(c, reqMap)
	if err != nil {
		return false, "系统错误: " + err.Error()
	}

	return success, message
}

// GetEndpointNotice 获取终端公告
func (e *endpointSvc) GetEndpointNotice(c *gin.Context, ts, count int) (*dto.EndpointNoticeResponse, error) {
	// 调用repository层获取终端公告信息
	repoResult, err := e.endpointRepo.GetEndpointNotices(c, int64(ts), count)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端公告列表失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if repoResult == nil {
		return nil, nil
	}

	// 转换为handler层DTO格式
	data := make([]dto.NoticeItem, len(repoResult.Data))
	for i, notice := range repoResult.Data {
		data[i] = dto.NoticeItem{
			ID:        notice.ID,
			Content:   notice.Content,
			Title:     notice.Title,
			Author:    notice.Author,
			CreatedAt: notice.CreatedAt,
		}
	}

	return &dto.EndpointNoticeResponse{
		Data:  data,
		Size:  repoResult.Size,
		IsEnd: repoResult.IsEnd,
	}, nil
}

// GetEndpointNoticeById 根据ID获取终端公告
func (e *endpointSvc) GetEndpointNoticeById(c *gin.Context, noticeID string) (*dto.NoticeItem, error) {
	// 调用repository层根据ID获取终端公告信息
	repoResult, err := e.endpointRepo.GetEndpointNoticeByID(c, noticeID)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端公告详情失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if repoResult == nil {
		return nil, nil
	}

	// 转换为handler层DTO格式
	return &dto.NoticeItem{
		ID:        repoResult.ID,
		Content:   repoResult.Content,
		Title:     repoResult.Title,
		Author:    repoResult.Author,
		CreatedAt: repoResult.CreatedAt,
	}, nil
}

// GetEndpointInfoByUsername 根据用户名获取终端信息
func (e *endpointSvc) GetEndpointInfoByUsername(c *gin.Context, username string) (*dto.EndpointInfoByUsernameResponse, error) {
	// 调用repository层根据用户名获取终端信息
	repoResult, err := e.endpointRepo.GetEndpointInfoByUsername(c, username)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端信息失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if repoResult == nil {
		return nil, nil
	}

	// 转换为handler层DTO
	response := &dto.EndpointInfoByUsernameResponse{
		ID:           repoResult.ID,
		Name:         repoResult.Name,
		Address:      repoResult.Address,
		Manager:      "",
		Phone:        "",
		TopAgency:    "",
		SecondAgency: "",
	}

	// 处理可能为空的字段
	if repoResult.TopAgency != nil {
		response.TopAgency = *repoResult.TopAgency
	}
	if repoResult.SecondAgency != nil {
		response.SecondAgency = *repoResult.SecondAgency
	}
	if repoResult.Manager != nil {
		response.Manager = *repoResult.Manager
	}
	if repoResult.Phone != nil {
		response.Phone = *repoResult.Phone
	}

	return response, nil
}

// GetEndpointRegion 获取终端区域
func (e *endpointSvc) GetEndpointRegion(c *gin.Context, uid int) (*dto.EndpointRegionResponse, error) {
	// 检查用户角色权限
	roles, err := e.adminUserRepo.CheckRole(c, uid, -1)
	if err != nil {
		log.Error("检查用户角色失败", zap.Int("uid", uid), zap.Error(err))
		return nil, errors.Wrap(err, "检查用户角色失败")
	}

	// 将roles转换为roleKey格式
	roleKey := map[string]interface{}{}
	if len(roles) > 0 {
		// 合并所有角色的slug作为角色标识
		var slugs []string
		for _, role := range roles {
			if role.Slug != "" {
				slugs = append(slugs, role.Slug)
			}
		}
		roleKey["slug"] = strings.Join(slugs, ",")
	}

	// 调用repository层获取终端区域信息
	repoResult, err := e.endpointRepo.GetEndpointRegion(c, roleKey, uid)
	if err != nil {
		log.Error("查询终端区域失败", zap.Int("uid", uid), zap.Error(err))
		return nil, errors.Wrap(err, "查询终端区域失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if len(repoResult) == 0 {
		return nil, nil
	}

	// 转换为handler层DTO格式
	data := make([]dto.RegionItem, len(repoResult))
	for i, item := range repoResult {
		data[i] = dto.RegionItem{
			ID:    item.ID,
			PID:   item.PID,
			Name:  item.Name,
			Level: item.Level,
		}
	}

	return &dto.EndpointRegionResponse{
		Data: data,
	}, nil
}

// GetEndpointStat 获取终端统计
func (e *endpointSvc) GetEndpointStat(c *gin.Context, topAgency, secondAgency string) (*dto.EndpointStatResponse, error) {
	// 调用repository层获取终端统计信息
	repoResult, err := e.endpointRepo.GetEndpointStat(c, topAgency, secondAgency)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端统计失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if len(repoResult) == 0 {
		return nil, nil
	}

	// 转换为handler层DTO格式，并将类型代码转换为中文描述
	data := make([]dto.StatItem, len(repoResult))
	for i, stat := range repoResult {
		typeDesc := ""
		switch stat.Type {
		case 1:
			typeDesc = "专柜"
		case 3:
			typeDesc = "专卖店"
		case 4:
			typeDesc = "城市综合体"
		default:
			typeDesc = "其他" // 对于其他类型的兜底处理
		}

		data[i] = dto.StatItem{
			Type:  typeDesc,
			Total: stat.Total,
		}
	}

	return &dto.EndpointStatResponse{
		Data: data,
	}, nil
}

// checkRoles 检查用户角色
func (e *endpointSvc) checkRoles(c *gin.Context, uid int, appType int) ([]string, error) {
	roles, err := e.adminUserRepo.CheckRole(c, uid, appType)
	if err != nil || len(roles) == 0 {
		return nil, errors.New("未查到用户权限")
	}

	var roleKeys []string
	for _, role := range roles {
		roleKeys = append(roleKeys, role.Slug)
	}

	return roleKeys, nil
}

// GetEndpointDetail 获取终端详情
func (e *endpointSvc) GetEndpointDetail(c *gin.Context, uid int, topAgency, secondAgency string, page, count int) (*dto.EndpointDetailResponse, error) {
	// 权限检查
	roleKeys, err := e.checkRoles(c, uid, -1)
	if err != nil {
		return nil, errors.Wrap(err, "权限不足")
	}

	// 根据用户角色验证参数
	hasTopAgencyRole := false
	hasSecondAgencyRole := false
	for _, roleKey := range roleKeys {
		if strings.Contains(roleKey, "topAgency") {
			hasTopAgencyRole = true
		}
		if strings.Contains(roleKey, "secondAgency") {
			hasSecondAgencyRole = true
		}
	}

	// 参数验证
	if hasTopAgencyRole && topAgency == "" {
		return nil, errors.New("总代topAgency参数不能为空")
	}
	if hasSecondAgencyRole && secondAgency == "" {
		return nil, errors.New("二代second_agency参数不能为空")
	}

	// 调用repository层获取数据
	repoResult, err := e.endpointRepo.GetEndpointDetail(c, topAgency, secondAgency, page, count)
	if err != nil {
		return nil, errors.Wrap(err, "查询终端详情失败")
	}

	// 如果没有找到数据，返回nil（由handler层处理"未找到数据"错误）
	if repoResult == nil {
		return nil, nil
	}

	// 转换为handler层DTO格式，并进行数据转换
	details := make([]dto.DetailItem, len(repoResult.Detail))
	for i, item := range repoResult.Detail {
		// 类型转换
		typeDesc := ""
		switch item.Type {
		case 1:
			typeDesc = "专柜"
		case 3:
			typeDesc = "专卖店"
		case 4:
			typeDesc = "城市综合体"
		default:
			typeDesc = "其他"
		}

		// belong字段转换
		belong := ""
		if item.SecondAgency == 0 {
			belong = "一级代理"
		} else {
			belong = "二级代理"
		}

		details[i] = dto.DetailItem{
			Name:   item.Name,
			Type:   typeDesc,
			Belong: belong,
		}
	}

	return &dto.EndpointDetailResponse{
		IsEnd:  repoResult.IsEnd,
		Detail: details,
	}, nil
}

// GetSalesStat 获取销售统计数据
func (e *endpointSvc) GetSalesStat(c *gin.Context, accessToken string) (*dto.SalesStatResponse, error) {
	// 调用外部API获取销售统计数据
	salesData, err := e.salesStatClient.GetSalesStat(c, accessToken)
	if err != nil {
		return nil, errors.Wrap(err, "获取销售统计数据失败")
	}

	// 构建响应数据（与Python版本保持一致）
	response := &dto.SalesStatResponse{
		Data:                     salesData["data"],
		StatisticsInstructionURL: "https://yx.readboy.com/static/statistics/explain/index.html",
		AssessmentInstructionURL: "https://yx.readboy.com/static/assessment/explain/index.html",
		StatisticsURL:            "https://h5-yx.readboy.com/admin#/auth/token?origin=care_app&redirect=/statistics/warranty-card&access_token=" + accessToken,
		AssessmentURL:            "https://h5-yx.readboy.com/admin#/auth/token?origin=care_app&redirect=/assessment/warranty-card&access_token=" + accessToken,
		TerminalURL:              "https://h5-yx.readboy.com/admin/#/auth/token?origin=care_app&redirect=/terminal/warranty-card&access_token=" + accessToken,
	}

	return response, nil
}
