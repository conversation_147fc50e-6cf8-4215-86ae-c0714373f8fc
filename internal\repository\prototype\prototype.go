package prototype

import (
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/machine"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	"marketing-app/internal/repository/prototype/builder"
	"marketing-app/internal/repository/prototype/dto"
	warrantyRepo "marketing-app/internal/repository/warranty/base"
	warrantyBuilder "marketing-app/internal/repository/warranty/base/builder"
	dto2 "marketing-app/internal/repository/warranty/base/dto"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type Prototype interface {
	Create(c *gin.Context, create *model.Prototype) error
	Get(c *gin.Context, query *builder.Prototype) (data *dto.Prototype, err error)
	Update(c *gin.Context, cond *builder.Prototype, upd map[string]interface{}) (affected int64, err error)
	GetPrototypeMachineTypeConfig(c *gin.Context, query *builder.Prototype) (data *dto.PrototypeMachineTypeConfig, err error)
	GetPrototypeMachineType(c *gin.Context, query *builder.Prototype) (data *dto.Prototype, err error)
	GetWarrantyReturns(c *gin.Context, barcode string) ([]dto.WarrantyReturn, error)
	GetPrototypeLimit(c *gin.Context, endpointId int) (*dto.PrototypeLimitInfo, error)
	GetPrototypeCount(c *gin.Context, endpointId int) (int64, int64, error)
	GetAgencyPrototypeCount(c *gin.Context, topAgency, secondAgency int) (int64, int64, error)
	GetAgencyPrototypeUserList(c *gin.Context, topAgency, secondAgency int) ([]dto.PrototypeUserInfo, error)
	GetPrototypeUserByPhone(c *gin.Context, phone string) (*dto.PrototypeUserDetail, error)
	BindPrototypeUser(c *gin.Context, phone string, endpointID int) (*dto.PrototypeUserBind, error)
	ChangePrototypeUserPhone(c *gin.Context, endpointID int, oldPhone, newPhone string) error
	DeletePrototypeUser(c *gin.Context, endpointID int, phone string) error
	ModelCanBePrototype(c *gin.Context, modelID int) (bool, error)
	CreateTransaction(c *gin.Context, barcode string, endpointID int, uid int, protoInfo *dto.PrototypeMachineTypeConfig, mes map[string]interface{}, drpMachineRepo machine.Machine) error
	GetPrototypeMachineCategory(c *gin.Context) ([]dto.ModelCategory, error)
	GetPrototypeList(c *gin.Context, endpointID int, pageSize, page int) (*dto.PrototypeListResult, error)
	GetPrototypeInfo(c *gin.Context, barcode string) (*dto.PrototypeInfo, error)
	DeletePrototype(c *gin.Context, barcode string, endpointId int) (int, string, error)
	QueryPrototype(c *gin.Context, barcode, number, imei string) (*model.Prototype, error)
	PrototypeWarrantyQuery(c *gin.Context, barcode, number, imei string, warrantyRepo warrantyRepo.Warranty) (*dto.PrototypeWarrantyQueryResult, error)
}

type prototype struct {
	db *gorm.DB
}

func NewPrototypeRepo(db *gorm.DB) Prototype {
	return &prototype{
		db: db,
	}
}

func (p *prototype) Create(c *gin.Context, create *model.Prototype) error {
	return p.db.WithContext(c).Model(&model.Prototype{}).Create(create).Error
}

func (p *prototype) Get(c *gin.Context, query *builder.Prototype) (data *dto.Prototype, err error) {
	err = query.Fill(p.db.WithContext(c).Model(&model.Prototype{})).Find(&data).Error
	return data, err
}

func (p *prototype) Update(c *gin.Context, cond *builder.Prototype, upd map[string]interface{}) (affected int64, err error) {
	res := cond.Fill(p.db.WithContext(c).Model(&model.Prototype{})).Updates(upd)
	return res.RowsAffected, res.Error
}

func (p *prototype) GetPrototypeMachineTypeConfig(c *gin.Context, query *builder.Prototype) (data *dto.PrototypeMachineTypeConfig, err error) {
	err = query.Fill(p.db.WithContext(c).Model(&model.Prototype{}).
		Select("prototype.*, mt.category_id, COALESCE(pc.discontinued, 0) AS discontinued")).
		First(&data).Error
	return data, err
}

func (p *prototype) GetPrototypeMachineType(c *gin.Context, query *builder.Prototype) (data *dto.Prototype, err error) {
	err = query.Fill(p.db.WithContext(c).Model(&model.Prototype{}).
		Select("prototype.*, mt.category_id")).
		Find(&data).Error
	return data, err
}

func (p *prototype) GetPrototypeLimit(c *gin.Context, endpointId int) (*dto.PrototypeLimitInfo, error) {
	var r dto.PrototypeLimitInfo
	err := p.db.WithContext(c).Table("endpoint_setting").
		Select("prototype_limit, prototype_frequency_limit").
		Where("endpoint_id = ?", endpointId).Take(&r).Error
	if err != nil {
		if isNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &r, nil
}

func (p *prototype) GetPrototypeCount(c *gin.Context, endpointId int) (int64, int64, error) {
	var total int64
	if err := p.db.WithContext(c).Table("prototype").Where("endpoint = ? AND status = 1", endpointId).Count(&total).Error; err != nil {
		return 0, 0, err
	}
	// month count
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)
	var monthTotal int64
	if err := p.db.Table("prototype").Where("endpoint = ? AND status = 1 AND created_at BETWEEN ? AND ?", endpointId, monthStart, monthEnd).Count(&monthTotal).Error; err != nil {
		return 0, 0, err
	}
	return total, monthTotal, nil
}

func (p *prototype) GetWarrantyReturns(c *gin.Context, barcode string) ([]dto.WarrantyReturn, error) {
	var wrs []dto.WarrantyReturn
	err := p.db.WithContext(c).Table("warranty_return wr").
		Select("wr.barcode, w.ext_barcode, wr.reason, wr.return_at, wr.created_at, wr.return_at AS order_time, "+
			"e.name as endpoint_name, e.address as endpoint_address, e.manager as endpoint_manager, "+
			"e.phone as manager_phone, 9 as data_type").
		Joins("LEFT JOIN endpoint e ON wr.endpoint = e.id").
		Joins("LEFT JOIN warranty w ON wr.warranty_id = w.id").
		Where("wr.barcode = ?", barcode).
		Find(&wrs).Error
	if err != nil {
		return nil, err
	}
	return wrs, nil
}

func (p *prototype) ModelCanBePrototype(c *gin.Context, modelID int) (bool, error) {
	type row struct {
		CategoryID       int    `gorm:"column:category_id"`
		PrototypeApkPath string `gorm:"column:prototype_apk_path"`
		PrototypeStatus  int    `gorm:"column:prototype_status"`
	}
	var r row
	err := p.db.WithContext(c).Table("machine_type").
		Select("category_id, prototype_apk_path, prototype_status").
		Where("prototype_status = 1 AND model_id = ?", modelID).Take(&r).Error
	if err != nil {
		if isNotFound(err) {
			return false, nil
		}
		return false, err
	}
	switch r.CategoryID {
	case 1, 2:
		return r.PrototypeApkPath != "", nil
	case 3, 8:
		return true, nil
	default:
		return true, nil
	}
}

func (p *prototype) CreateTransaction(c *gin.Context, barcode string, endpointID int, uid int, protoInfo *dto.PrototypeMachineTypeConfig, mes map[string]interface{}, drpMachineRepo machine.Machine) error {
	modelName := mes["model"].(string)
	number := mes["number"].(string)
	imei := mes["imei1"].(string)
	modelID, _ := strconv.Atoi(mes["model_id"].(string))

	tx := p.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	now := time.Now()
	protoType := 0
	if protoInfo != nil && protoInfo.Status == 1 {
		protoType = protoInfo.Type
		if err := tx.Table("prototype").Where("barcode = ? AND status = 1", barcode).
			Updates(map[string]interface{}{"status": 0, "removed_at": now, "updated_at": now}).Error; err != nil {
			tx.Rollback()
			return err
		}
		// 进销存同步
		err := drpMachineRepo.DrpMachine(
			c,
			drpMachineBuilder.NewDrpMachine().
				BarcodeEq(barcode).
				StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
				OperationTypeEq(5).
				ReturnEq(0),
		)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	newRow := map[string]interface{}{
		"number":     number,
		"barcode":    barcode,
		"imei":       imei,
		"endpoint":   endpointID,
		"status":     1,
		"model":      modelName,
		"model_id":   modelID,
		"user_id":    uid,
		"created_at": now,
		"type":       protoType,
	}
	if err := tx.Table("prototype").Create(newRow).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (p *prototype) GetPrototypeMachineCategory(c *gin.Context) ([]dto.ModelCategory, error) {
	var result []dto.ModelCategory
	err := p.db.WithContext(c).Table("model_category mc").
		Select("DISTINCT mc.id, mc.name").
		Joins("LEFT JOIN machine_type mt ON mt.category_id = mc.id").
		Where("mt.prototype_status = 1").
		Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (p *prototype) GetPrototypeList(c *gin.Context, endpointID int, pageSize, page int) (*dto.PrototypeListResult, error) {
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}
	// 查询样机列表
	var prototypes []dto.PrototypeListItem
	err := p.db.WithContext(c).Table("prototype p").
		Select("p.barcode, p.status, p.tablet_status as machine_status, p.model, mc.name, mc.id, p.type, p.created_at, au.name as user_name, au.username, au.phone").
		Joins("LEFT JOIN machine_type m ON p.model_id = m.model_id").
		Joins("LEFT JOIN model_category mc ON m.category_id = mc.id").
		Joins("LEFT JOIN admin_users au ON au.id = p.user_id").
		Where("p.endpoint = ? AND p.status = 1", endpointID).
		Order("p.created_at DESC").
		Limit(pageSize + 1).
		Offset(offset).
		Find(&prototypes).Error
	if err != nil {
		return nil, err
	}
	if len(prototypes) == 0 {
		return nil, nil
	}
	// 处理返回数据
	isEnd := len(prototypes) < pageSize+1
	size := len(prototypes)
	if !isEnd {
		size = pageSize
		prototypes = prototypes[:size]
	}
	var resultData []map[string]interface{}
	for _, item := range prototypes {
		data := map[string]interface{}{
			"barcode":        item.Barcode,
			"status":         item.Status,
			"machine_status": 1, // 固定为1
			"model":          item.Model,
			"name":           item.Name,
			"id":             item.ID,
			"type":           item.Type,
			"created_at":     item.CreatedAt.Format(time.DateTime),
			"user_name":      item.UserName,
			"username":       item.Username,
			"phone":          item.Phone,
		}
		// 处理换机和退机标记
		if item.Type == 2 && item.UserName != nil && *item.UserName != "" {
			userName := "(换机)" + *item.UserName
			data["user_name"] = userName
		} else if item.Type == 3 && item.UserName != nil && *item.UserName != "" {
			userName := "(退机)" + *item.UserName
			data["user_name"] = userName
		}

		resultData = append(resultData, data)
	}

	result := &dto.PrototypeListResult{
		Data:  resultData,
		Size:  size,
		IsEnd: isEnd,
	}

	return result, nil
}

func (p *prototype) GetPrototypeInfo(c *gin.Context, barcode string) (*dto.PrototypeInfo, error) {
	var pi dto.PrototypeInfo
	err := p.db.Table("prototype p").
		Select("p.number, p.status, p.type, p.endpoint, p.top_agency, p.second_agency, mt.category_id, COALESCE(pc.discontinued, 0) AS discontinued").
		Joins("LEFT JOIN machine_type mt ON p.model_id = mt.model_id").
		Joins("LEFT JOIN prototype_config pc ON p.model_id = pc.model_id").
		Where("p.barcode = ? AND p.status = 1", barcode).Take(&pi).Error
	if err != nil {
		if isNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &pi, nil
}

func (p *prototype) DeletePrototype(c *gin.Context, barcode string, endpointId int) (int, string, error) {
	now := time.Now()
	// 更新样机状态为离库
	result := p.db.WithContext(c).Table("prototype").
		Where("barcode = ? AND endpoint = ? AND status = 1", barcode, endpointId).
		Updates(map[string]interface{}{
			"status":     0,
			"removed_at": now,
			"updated_at": now,
		})

	if result.Error != nil {
		return 0, "系统错误", result.Error
	}
	if result.RowsAffected == 0 {
		return 0, "离库失败, 系统出错", nil
	}

	return 1, "样机离库成功", nil
}

// GetAgencyPrototypeCount 获取代理商样机统计
func (p *prototype) GetAgencyPrototypeCount(c *gin.Context, topAgency, secondAgency int) (int64, int64, error) {
	// 总样机数
	var total int64
	err := p.db.WithContext(c).Table("prototype").
		Where("top_agency = ? AND second_agency = ? AND endpoint = 0 AND status = 1", topAgency, secondAgency).
		Count(&total).Error
	if err != nil {
		return 0, 0, err
	}

	// 月样机数
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)
	var monthTotal int64
	err = p.db.WithContext(c).Table("prototype").
		Where("top_agency = ? AND second_agency = ? AND endpoint = 0 AND status = 1 AND created_at BETWEEN ? AND ?",
			topAgency, secondAgency, monthStart, monthEnd).
		Count(&monthTotal).Error
	if err != nil {
		return 0, 0, err
	}

	return total, monthTotal, nil
}

// GetAgencyPrototypeUserList 获取代理商演示用户列表
func (p *prototype) GetAgencyPrototypeUserList(c *gin.Context, topAgency, secondAgency int) ([]dto.PrototypeUserInfo, error) {
	var users []dto.PrototypeUserInfo
	err := p.db.WithContext(c).Table("prototype_users").
		Select("id, phone").
		Where("top_agency = ? AND second_agency = ? AND endpoint IS NULL", topAgency, secondAgency).
		Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// GetPrototypeUserByPhone 根据手机号查询演示用户
func (p *prototype) GetPrototypeUserByPhone(c *gin.Context, phone string) (*dto.PrototypeUserDetail, error) {
	var user dto.PrototypeUserDetail
	err := p.db.WithContext(c).Table("prototype_users").
		Select("id, top_agency, second_agency, endpoint, phone").
		Where("phone = ?", phone).
		First(&user).Error
	if err != nil {
		if isNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// BindPrototypeUser 绑定演示用户到终端
func (p *prototype) BindPrototypeUser(c *gin.Context, phone string, endpointID int) (*dto.PrototypeUserBind, error) {
	now := time.Now()

	// 检查当前终端已绑定的用户数量
	var count int64
	err := p.db.WithContext(c).Table("prototype_users").
		Where("endpoint = ?", endpointID).
		Count(&count).Error
	if err != nil {
		return nil, err
	}

	// 如果当前终端已绑定5个用户，返回错误
	if count >= 5 {
		return nil, errors.New("当前终端已绑定5个用户，无法继续绑定")
	}

	// 先查询用户是否存在
	existingUser, err := p.GetPrototypeUserByPhone(c, phone)
	if err != nil {
		return nil, err
	}

	if existingUser != nil {
		// 检查是否已经绑定到同一个终端
		if existingUser.Endpoint != nil && *existingUser.Endpoint == endpointID {
			return nil, errors.New("此终端已绑定此账号，无需二次操作")
		}
		// 更新现有用户，将代理商字段设置为NULL
		err = p.db.WithContext(c).Table("prototype_users").
			Where("phone = ?", phone).
			Updates(map[string]interface{}{
				"top_agency":    nil,
				"second_agency": nil,
				"endpoint":      endpointID,
				"created_at":    now,
				"updated_at":    now,
			}).Error
		if err != nil {
			return nil, err
		}
	} else {
		// 插入新用户
		err = p.db.WithContext(c).Table("prototype_users").
			Create(map[string]interface{}{
				"phone":      phone,
				"endpoint":   endpointID,
				"created_at": now,
				"updated_at": now,
			}).Error
		if err != nil {
			return nil, err
		}
	}

	// 返回绑定后的用户信息
	user, err := p.GetPrototypeUserByPhone(c, phone)
	if err != nil {
		return nil, err
	}

	return &dto.PrototypeUserBind{
		ID:         user.ID,
		Phone:      user.Phone,
		EndpointID: endpointID,
	}, nil
}

// ChangePrototypeUserPhone 修改演示用户手机号
func (p *prototype) ChangePrototypeUserPhone(c *gin.Context, endpointID int, oldPhone, newPhone string) error {
	// 更新验证码属于新号码
	result := p.db.WithContext(c).Table("prototype_users").
		Where("endpoint = ? AND phone = ?", endpointID, oldPhone).
		Update("phone", newPhone)

	if result.Error != nil {
		return result.Error
	}

	// 检查是否成功更新了记录
	if result.RowsAffected == 0 {
		return errors.New("未找到匹配的记录")
	}

	return nil
}

// DeletePrototypeUser 删除演示用户
func (p *prototype) DeletePrototypeUser(c *gin.Context, endpointID int, phone string) error {
	// 执行删除操作
	result := p.db.WithContext(c).Table("prototype_users").
		Where("endpoint = ? AND phone = ?", endpointID, phone).
		Delete(&struct{}{})

	if result.Error != nil {
		return result.Error
	}

	// 检查是否成功删除了记录
	if result.RowsAffected == 0 {
		return errors.New("未找到匹配的记录")
	}

	return nil
}

// QueryPrototype 查询样机信息 - 优先匹配barcode，其次imei，最后number
func (p *prototype) QueryPrototype(c *gin.Context, barcode, number, imei string) (*model.Prototype, error) {
	var pt model.Prototype
	var err error

	if barcode != "" {
		err = p.db.WithContext(c).Where("barcode = ? AND status = 1", barcode).First(&pt).Error
	} else if imei != "" {
		err = p.db.WithContext(c).Where("imei = ? AND status = 1", imei).First(&pt).Error
	} else if number != "" {
		err = p.db.WithContext(c).Where("number = ? AND status = 1", number).First(&pt).Error
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没找到返回nil，不是错误
		}
		return nil, err
	}

	return &pt, nil
}

// PrototypeWarrantyQuery 查询样机是否有保卡或者已经入样机库
func (p *prototype) PrototypeWarrantyQuery(c *gin.Context, barcode, number, imei string, warrantyRepo warrantyRepo.Warranty) (*dto.PrototypeWarrantyQueryResult, error) {
	// 验证参数
	if barcode == "" && number == "" {
		return nil, errors.New("参数错误")
	}

	// 查询保卡信息
	var wt *dto2.WarrantyMachineType
	var err error
	warranty, err := warrantyRepo.GetWarrantyMachineType(
		c,
		warrantyBuilder.NewWarranty().
			BarcodeOrExtBarcodeEq(barcode, barcode).
			Imeis(imei).
			NumberEq(number).
			StatusIn(consts.WarrantyStatusActive, consts.WarrantyStatusVirtual).
			JoinMachineType("LEFT JOIN machine_type mt ON warranty.model_id = mt.model_id"),
	)
	if err != nil {
		return nil, errors.Wrap(err, "查询保卡信息失败")
	}

	// 处理保卡数据
	var hasWarranty int
	if warranty.Id == 0 {
		hasWarranty = 0
	} else if warranty.Status == 1 {
		hasWarranty = 1
		wt = &warranty
	} else {
		hasWarranty = 0
	}

	// 查询样机信息
	prototypeModel, err := p.QueryPrototype(c, barcode, number, imei)
	if err != nil {
		return nil, errors.Wrap(err, "查询样机信息失败")
	}

	// 转换样机数据为DTO格式
	var prototypeData *dto.Prototype
	var hasPrototype int
	if prototypeModel != nil {
		hasPrototype = 1
		prototypeData = &dto.Prototype{
			ID:           prototypeModel.ID,
			ModelID:      prototypeModel.ModelID,
			Model:        prototypeModel.Model,
			Barcode:      prototypeModel.Barcode,
			Number:       prototypeModel.Number,
			Imei:         prototypeModel.Imei,
			TopAgency:    prototypeModel.TopAgency,
			SecondAgency: prototypeModel.SecondAgency,
			Endpoint:     prototypeModel.Endpoint,
			UserID:       prototypeModel.UserID,
			Type:         prototypeModel.Type,
			Status:       prototypeModel.Status,
			TabletStatus: prototypeModel.TabletStatus,
			CreatedAt:    prototypeModel.CreatedAt,
			UpdatedAt:    prototypeModel.UpdatedAt,
		}
	} else {
		hasPrototype = 0
	}

	// 构造返回结果，对应Python的ret字典
	result := &dto.PrototypeWarrantyQueryResult{
		Warranty:      wt,
		HasWarranty:   hasWarranty,
		HasPrototype:  hasPrototype,
		PrototypeData: prototypeData,
	}

	return result, nil
}

func isNotFound(err error) bool { return errors.Is(err, gorm.ErrRecordNotFound) }
