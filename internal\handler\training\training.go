package training

import (
	"encoding/json"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/training/dto"
	"marketing-app/internal/service/training"
	"marketing-app/internal/service/training/entity"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

type Training interface {
	GetTrainVideos(c *gin.Context)
	GetTrainList(c *gin.Context)
	GetTrainTypes(c *gin.Context)
	GetTrainCategories(c *gin.Context)
	GetTrainTypeCategories(c *gin.Context)
	AddLearningHistory(c *gin.Context)
	AddShareHistory(c *gin.Context)
	SearchTrains(c *gin.Context)
	GetTrainHot(c *gin.Context)
	AddCollection(c *gin.Context)
	CancelCollection(c *gin.Context)
	GetCollectionList(c *gin.Context)
	GetTrainBanner(c *gin.Context)
}

type trainingHandler struct {
	trainingSvc training.TrainingSvc
}

func NewTrainingHandler(svc training.TrainingSvc) Training {
	return &trainingHandler{trainingSvc: svc}
}

// GetTrainVideos 获取训练视频列表
func (t *trainingHandler) GetTrainVideos(c *gin.Context) {
	var (
		req  dto.TrainVideosRequest
		err  error
		resp *dto.TrainVideosResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TrainVideosRequest{
		Ts:    req.Ts,
		Count: req.Count,
	}

	// 调用服务层获取训练视频
	r, err := t.trainingSvc.GetTrainVideos(c, serviceReq)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if r == nil {
		err = errors.New("没有发布任何视频")
		return
	}

	// 转换为响应DTO
	data := make([]dto.TrainVideoItem, len(r.Data))
	for i, item := range r.Data {
		// 处理预览图
		var previews []string
		if item.Preview != "" {
			// 尝试解析预览图JSON
			if err := json.Unmarshal([]byte(item.Preview), &previews); err != nil {
				previews = nil
			}
		}

		data[i] = dto.TrainVideoItem{
			ID:          item.ID,
			Title:       item.Title,
			Preview:     previews,
			Path:        item.Path,
			Description: item.Description,
			CreatedAt:   item.CreatedAt.Format("2006.01.02 15:04:05"),
			UpdatedAt:   item.UpdatedAt.Format("2006.01.02 15:04:05"),
		}
	}

	resp = &dto.TrainVideosResponse{
		Data:  data,
		Size:  r.Size,
		IsEnd: r.IsEnd,
	}
}

// GetTrainList 获取培训列表
func (t *trainingHandler) GetTrainList(c *gin.Context) {
	var (
		req  dto.TrainListRequest
		err  error
		resp *dto.TrainListResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	// 转换为service层实体（默认值处理交给service层）
	serviceReq := &entity.TrainListRequest{
		Page:     req.Page,
		Count:    req.Count,
		Type:     req.Type,
		Category: req.Category,
		Order:    req.Order,
		UID:      uid,
	}

	// 调用服务层获取培训列表
	r, err := t.trainingSvc.GetTrainList(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	data := make([]dto.TrainListItem, len(r.Data))
	for i, item := range r.Data {
		// 处理预览图
		var previews []string
		if item.Preview != "" {
			// 尝试解析预览图JSON
			if err := json.Unmarshal([]byte(item.Preview), &previews); err != nil {
				previews = nil
			}
		}

		// 判断是否已读
		isRead := uint8(0)
		if item.ReadTime != nil && item.ReadTime.Valid {
			isRead = 1
		}

		data[i] = dto.TrainListItem{
			ID:                 item.ID,
			Name:               item.Name,
			Preview:            previews,
			Path:               item.Path,
			ArticleLink:        item.ArticleLink,
			Share:              item.Share,
			Description:        item.Description,
			CreatedAt:          item.CreatedAt.Format("2006/01/02 15:04:05"),
			UpdatedAt:          item.UpdatedAt.Format("2006/01/02 15:04:05"),
			Star:               item.Star,
			DownloadCount:      item.DownloadCount,
			Credit:             item.Credit,
			CreditLearningTime: item.CreditLearningTime,
			Collection:         item.Collection,
			GetCredit:          item.GetCredit,
			CollectionCount:    item.CollectionCount,
			ShareURL:           item.ShareURL,
			IsRead:             isRead,
		}
	}

	resp = &dto.TrainListResponse{
		Data:  data,
		Size:  r.Size,
		IsEnd: r.IsEnd,
	}
}

// GetTrainTypes 获取培训类型列表
func (t *trainingHandler) GetTrainTypes(c *gin.Context) {
	var (
		err  error
		resp []dto.TrainTypeItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 调用服务层获取培训类型
	r, err := t.trainingSvc.GetTrainTypes(c)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if len(r) == 0 {
		err = errors.New("未找到数据")
		return
	}

	// 转换为响应DTO
	resp = make([]dto.TrainTypeItem, len(r))
	for i, item := range r {
		resp[i] = dto.TrainTypeItem{
			ID:    item.ID,
			Title: item.Title,
		}
	}
}

// GetTrainCategories 获取培训分类列表
func (t *trainingHandler) GetTrainCategories(c *gin.Context) {
	var (
		req  dto.TrainCategoriesRequest
		err  error
		resp []dto.TrainCategoryItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TrainCategoriesRequest{
		Type: req.Type,
	}

	// 调用服务层获取培训分类
	r, err := t.trainingSvc.GetTrainCategories(c, serviceReq)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if len(r) == 0 {
		err = errors.New("未找到数据")
		return
	}

	// 转换为响应DTO
	resp = make([]dto.TrainCategoryItem, len(r))
	for i, item := range r {
		resp[i] = dto.TrainCategoryItem{
			ID:    item.ID,
			Title: item.Title,
		}
	}
}

// GetTrainTypeCategories 获取培训类型分类关系
func (t *trainingHandler) GetTrainTypeCategories(c *gin.Context) {
	var (
		err  error
		resp []dto.TrainTypeCategoryItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 调用服务层获取培训类型分类关系
	r, err := t.trainingSvc.GetTrainTypeCategories(c)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if len(r) == 0 {
		err = errors.New("未找到数据")
		return
	}

	// 转换为响应DTO
	resp = make([]dto.TrainTypeCategoryItem, len(r))
	for i, item := range r {
		resp[i] = dto.TrainTypeCategoryItem{
			Type:     item.Type,
			Category: item.Category,
			Title:    item.Title,
		}
	}
}

// AddLearningHistory 添加学习历史
func (t *trainingHandler) AddLearningHistory(c *gin.Context) {
	var (
		req dto.AddLearningHistoryRequest
		err error
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, true)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.PostForm("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.AddLearningHistoryRequest{
		ID:           req.ID,
		LearningTime: req.LearningTime,
		UID:          uid,
	}

	// 调用服务层添加学习历史
	if err = t.trainingSvc.AddLearningHistory(c, serviceReq); err != nil {
		return
	}
}

// AddShareHistory 添加分享历史
func (t *trainingHandler) AddShareHistory(c *gin.Context) {
	var (
		req dto.AddShareHistoryRequest
		err error
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, true)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.PostForm("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.AddShareHistoryRequest{
		TrainID: req.TrainID,
		UID:     uid,
	}

	// 调用服务层添加分享历史
	result, serviceErr := t.trainingSvc.AddShareHistory(c, serviceReq)
	if serviceErr != nil {
		err = serviceErr
		return
	}

	// 根据Python版本的逻辑处理返回值
	if result == -1 {
		err = errors.New("此信息不可被分享")
		return
	}
	if result == 0 {
		err = errors.New("系统内部错误")
		return
	}
}

// SearchTrains 搜索培训
func (t *trainingHandler) SearchTrains(c *gin.Context) {
	var (
		req  dto.TrainSearchRequest
		err  error
		resp []dto.TrainSearchItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TrainSearchRequest{
		Keyword: req.Keyword,
		UID:     uid,
	}

	// 调用服务层搜索培训
	r, err := t.trainingSvc.SearchTrains(c, serviceReq)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if len(r) == 0 {
		err = errors.New("未找到数据")
		return
	}

	// 转换为响应DTO
	resp = make([]dto.TrainSearchItem, len(r))
	for i, item := range r {
		// 处理预览图
		var previews []string
		if item.Preview != "" {
			// 尝试解析预览图JSON
			if err := json.Unmarshal([]byte(item.Preview), &previews); err != nil {
				previews = nil
			}
		}

		resp[i] = dto.TrainSearchItem{
			ID:                 item.ID,
			Name:               item.Name,
			Preview:            previews,
			Path:               item.Path,
			ArticleLink:        item.ArticleLink,
			Share:              item.Share,
			Description:        item.Description,
			CreatedAt:          item.CreatedAt.Format("2006/01/02 15:04:05"),
			UpdatedAt:          item.UpdatedAt.Format("2006/01/02 15:04:05"),
			Star:               item.Star,
			DownloadCount:      item.DownloadCount,
			Credit:             item.Credit,
			CreditLearningTime: item.CreditLearningTime,
			GetCredit:          item.GetCredit,
			ShareURL:           item.ShareURL,
		}
	}
}

// GetTrainHot 获取热门培训列表
func (t *trainingHandler) GetTrainHot(c *gin.Context) {
	var (
		req  dto.TrainHotRequest
		err  error
		resp *dto.TrainHotResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 转换为service层实体（默认值处理交给service层）
	serviceReq := &entity.TrainHotRequest{
		Page:  req.Page,
		Count: req.Count,
	}

	// 调用服务层获取热门培训
	r, err := t.trainingSvc.GetTrainHot(c, serviceReq)
	if err != nil {
		return
	}

	// 如果没有找到数据，返回错误
	if r == nil {
		err = errors.New("未找到数据")
		return
	}

	// 转换为响应DTO
	data := make([]dto.TrainHotItem, len(r.Data))
	for i, item := range r.Data {
		// 处理预览图
		var previews []string
		if item.Preview != "" {
			// 尝试解析预览图JSON
			if err := json.Unmarshal([]byte(item.Preview), &previews); err != nil {
				previews = nil
			}
		}

		data[i] = dto.TrainHotItem{
			ID:            item.ID,
			Name:          item.Name,
			Preview:       previews,
			Path:          item.Path,
			Description:   item.Description,
			ArticleLink:   item.ArticleLink,
			Share:         item.Share,
			CreatedAt:     item.CreatedAt.Format("2006/01/02 15:04:05"),
			UpdatedAt:     item.UpdatedAt.Format("2006/01/02 15:04:05"),
			Star:          item.Star,
			DownloadCount: item.DownloadCount,
			ShareURL:      item.ShareURL,
		}
	}

	resp = &dto.TrainHotResponse{
		Data:  data,
		Size:  r.Size,
		IsEnd: r.IsEnd,
	}
}

// AddCollection 添加培训收藏
func (t *trainingHandler) AddCollection(c *gin.Context) {
	var (
		req dto.AddCollectionRequest
		err error
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, true)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.PostForm("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.AddCollectionRequest{
		TrainID: req.TrainID,
		UID:     uid,
	}

	// 调用服务层添加收藏
	if err = t.trainingSvc.AddCollection(c, serviceReq); err != nil {
		return
	}
}

// CancelCollection 取消培训收藏
func (t *trainingHandler) CancelCollection(c *gin.Context) {
	var (
		req dto.CancelCollectionRequest
		err error
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, true)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.PostForm("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.CancelCollectionRequest{
		TrainID: req.TrainID,
		UID:     uid,
	}

	// 调用服务层取消收藏
	if err = t.trainingSvc.CancelCollection(c, serviceReq); err != nil {
		return
	}
}

// GetCollectionList 获取培训收藏列表
func (t *trainingHandler) GetCollectionList(c *gin.Context) {
	var (
		req  dto.CollectionListRequest
		err  error
		resp *dto.CollectionListResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体（默认值处理交给service层）
	serviceReq := &entity.CollectionListRequest{
		Page:  req.Page,
		Count: req.Count,
		UID:   uid,
	}

	// 调用服务层获取收藏列表
	r, err := t.trainingSvc.GetCollectionList(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	data := make([]dto.CollectionListItem, len(r.Data))
	for i, item := range r.Data {
		// 处理预览图
		var previews []string
		if item.Preview != "" {
			// 尝试解析预览图JSON
			if err := json.Unmarshal([]byte(item.Preview), &previews); err != nil {
				previews = nil
			}
		}

		// 判断是否已读
		isRead := uint8(0)
		if item.ReadTime != nil && item.ReadTime.Valid {
			isRead = 1
		}

		data[i] = dto.CollectionListItem{
			ID:              item.ID,
			Name:            item.Name,
			Preview:         previews,
			Path:            item.Path,
			ArticleLink:     item.ArticleLink,
			Share:           item.Share,
			Description:     item.Description,
			CreatedAt:       item.CreatedAt.Format("2006/01/02 15:04:05"),
			UpdatedAt:       item.UpdatedAt.Format("2006/01/02 15:04:05"),
			Star:            item.Star,
			DownloadCount:   item.DownloadCount,
			Collection:      item.Collection,
			CollectionCount: item.CollectionCount,
			ShareURL:        item.ShareURL,
			IsRead:          isRead,
		}
	}

	resp = &dto.CollectionListResponse{
		Data:  data,
		Size:  r.Size,
		IsEnd: r.IsEnd,
	}
}

// GetTrainBanner 获取培训Banner列表
func (t *trainingHandler) GetTrainBanner(c *gin.Context) {
	var (
		req  dto.TrainBannerRequest
		err  error
		resp []dto.TrainBannerItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定请求参数失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}

	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TrainBannerRequest{
		Type: req.Type,
		UID:  uid,
	}

	// 调用服务层获取Banner列表
	r, err := t.trainingSvc.GetTrainBanner(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = make([]dto.TrainBannerItem, len(r))
	for i, item := range r {
		// 处理预览图
		var previews []string
		if item.Preview != "" {
			// 尝试解析预览图JSON
			if err := json.Unmarshal([]byte(item.Preview), &previews); err != nil {
				previews = nil
			}
		}

		resp[i] = dto.TrainBannerItem{
			ID:            item.ID,
			Name:          item.Name,
			Preview:       previews,
			Path:          item.Path,
			ArticleLink:   item.ArticleLink,
			Share:         item.Share,
			Description:   item.Description,
			CreatedAt:     item.CreatedAt.Format("2006/01/02 15:04:05"),
			UpdatedAt:     item.UpdatedAt.Format("2006/01/02 15:04:05"),
			Star:          item.Star,
			DownloadCount: item.DownloadCount,
			BannerImage:   item.BannerImage,
			Collection:    item.Collection,
			ShareURL:      item.ShareURL,
		}
	}
}
