package dto

import (
	"time"
)

// CustomerListRequest 客户列表请求DTO
type CustomerListRequest struct {
	Page     int    `form:"page"`     // 页码，默认1
	Count    int    `form:"count"`    // 每页数量，默认10
	Status   *uint8 `form:"status"`   // 状态筛选
	Level    *uint8 `form:"level"`    // 等级筛选
	Region   string `form:"region"`   // 地区筛选
	Industry string `form:"industry"` // 行业筛选
	Source   string `form:"source"`   // 来源筛选
	Keyword  string `form:"keyword"`  // 关键词搜索
}

// CustomerListItem 客户列表项DTO
type CustomerListItem struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Phone       string `json:"phone"`
	Email       string `json:"email"`
	Company     string `json:"company"`
	Position    string `json:"position"`
	Region      string `json:"region"`
	Industry    string `json:"industry"`
	Source      string `json:"source"`
	Level       uint8  `json:"level"`
	Status      uint8  `json:"status"`
	Remark      string `json:"remark"`
	CreatedBy   uint   `json:"created_by"`
	UpdatedBy   uint   `json:"updated_by"`
	CreatedAt   string `json:"created_at"`   // 格式化后的创建时间
	UpdatedAt   string `json:"updated_at"`   // 格式化后的更新时间
	LastContact string `json:"last_contact"` // 格式化后的最后联系时间
}

// CustomerListResponse 客户列表响应DTO
type CustomerListResponse struct {
	Data  []CustomerListItem `json:"data"`
	Size  int                `json:"size"`
	IsEnd bool               `json:"isEnd"`
}

// CustomerDetailRequest 客户详情请求DTO
type CustomerDetailRequest struct {
	CustomerID uint `form:"customer_id" binding:"required"` // 客户ID
}

// CustomerDetailItem 客户详情项DTO
type CustomerDetailItem struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Phone       string `json:"phone"`
	Email       string `json:"email"`
	Company     string `json:"company"`
	Position    string `json:"position"`
	Region      string `json:"region"`
	Industry    string `json:"industry"`
	Source      string `json:"source"`
	Level       uint8  `json:"level"`
	Status      uint8  `json:"status"`
	Remark      string `json:"remark"`
	CreatedBy   uint   `json:"created_by"`
	UpdatedBy   uint   `json:"updated_by"`
	CreatedAt   string `json:"created_at"`   // 格式化后的创建时间
	UpdatedAt   string `json:"updated_at"`   // 格式化后的更新时间
	LastContact string `json:"last_contact"` // 格式化后的最后联系时间
}

// CreateCustomerRequest 创建客户请求DTO
type CreateCustomerRequest struct {
	Name        string     `form:"name" binding:"required"`                        // 客户姓名
	Phone       string     `form:"phone" binding:"required"`                       // 联系电话
	Email       string     `form:"email"`                                          // 邮箱地址
	Company     string     `form:"company"`                                        // 公司名称
	Position    string     `form:"position"`                                       // 职位
	Region      string     `form:"region"`                                         // 所在地区
	Industry    string     `form:"industry"`                                       // 所属行业
	Source      string     `form:"source"`                                         // 客户来源
	Level       uint8      `form:"level"`                                          // 客户等级
	Remark      string     `form:"remark"`                                         // 备注信息
	LastContact *time.Time `form:"last_contact" time_format:"2006-01-02 15:04:05"` // 最后联系时间
}

// UpdateCustomerRequest 更新客户请求DTO
type UpdateCustomerRequest struct {
	CustomerID  uint       `form:"customer_id" binding:"required"`                 // 客户ID
	Name        string     `form:"name"`                                           // 客户姓名
	Phone       string     `form:"phone"`                                          // 联系电话
	Email       string     `form:"email"`                                          // 邮箱地址
	Company     string     `form:"company"`                                        // 公司名称
	Position    string     `form:"position"`                                       // 职位
	Region      string     `form:"region"`                                         // 所在地区
	Industry    string     `form:"industry"`                                       // 所属行业
	Source      string     `form:"source"`                                         // 客户来源
	Level       uint8      `form:"level"`                                          // 客户等级
	Status      uint8      `form:"status"`                                         // 状态
	Remark      string     `form:"remark"`                                         // 备注信息
	LastContact *time.Time `form:"last_contact" time_format:"2006-01-02 15:04:05"` // 最后联系时间
}

// DeleteCustomerRequest 删除客户请求DTO
type DeleteCustomerRequest struct {
	CustomerID uint `form:"customer_id" binding:"required"` // 客户ID
}

// CustomerSearchRequest 客户搜索请求DTO
type CustomerSearchRequest struct {
	Keyword  string `form:"keyword" binding:"required"` // 搜索关键词
	Status   *uint8 `form:"status"`                     // 状态筛选
	Level    *uint8 `form:"level"`                      // 等级筛选
	Region   string `form:"region"`                     // 地区筛选
	Industry string `form:"industry"`                   // 行业筛选
	Source   string `form:"source"`                     // 来源筛选
}

// CustomerSearchItem 客户搜索项DTO
type CustomerSearchItem struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Phone       string `json:"phone"`
	Email       string `json:"email"`
	Company     string `json:"company"`
	Position    string `json:"position"`
	Region      string `json:"region"`
	Industry    string `json:"industry"`
	Source      string `json:"source"`
	Level       uint8  `json:"level"`
	Status      uint8  `json:"status"`
	CreatedAt   string `json:"created_at"`   // 格式化后的创建时间
	UpdatedAt   string `json:"updated_at"`   // 格式化后的更新时间
	LastContact string `json:"last_contact"` // 格式化后的最后联系时间
}

// CustomerContactListRequest 客户联系记录列表请求DTO
type CustomerContactListRequest struct {
	CustomerID uint `form:"customer_id" binding:"required"` // 客户ID
	Page       int  `form:"page"`                           // 页码，默认1
	Count      int  `form:"count"`                          // 每页数量，默认10
}

// CustomerContactItem 客户联系记录项DTO
type CustomerContactItem struct {
	ID         uint   `json:"id"`
	CustomerID uint   `json:"customer_id"`
	Type       uint8  `json:"type"`
	Content    string `json:"content"`
	Result     string `json:"result"`
	NextTime   string `json:"next_time"` // 格式化后的下次联系时间
	CreatedBy  uint   `json:"created_by"`
	CreatedAt  string `json:"created_at"` // 格式化后的创建时间
}

// CustomerContactListResponse 客户联系记录列表响应DTO
type CustomerContactListResponse struct {
	Data  []CustomerContactItem `json:"data"`
	Size  int                   `json:"size"`
	IsEnd bool                  `json:"isEnd"`
}

// CreateCustomerContactRequest 创建客户联系记录请求DTO
type CreateCustomerContactRequest struct {
	CustomerID uint       `form:"customer_id" binding:"required"`              // 客户ID
	Type       uint8      `form:"type" binding:"required"`                     // 联系类型
	Content    string     `form:"content" binding:"required"`                  // 联系内容
	Result     string     `form:"result"`                                      // 联系结果
	NextTime   *time.Time `form:"next_time" time_format:"2006-01-02 15:04:05"` // 下次联系时间
}

// UpdateCustomerContactRequest 更新客户联系记录请求DTO
type UpdateCustomerContactRequest struct {
	ContactID uint       `form:"contact_id" binding:"required"`               // 联系记录ID
	Type      uint8      `form:"type"`                                        // 联系类型
	Content   string     `form:"content"`                                     // 联系内容
	Result    string     `form:"result"`                                      // 联系结果
	NextTime  *time.Time `form:"next_time" time_format:"2006-01-02 15:04:05"` // 下次联系时间
}

// DeleteCustomerContactRequest 删除客户联系记录请求DTO
type DeleteCustomerContactRequest struct {
	ContactID uint `form:"contact_id" binding:"required"` // 联系记录ID
}

// CustomerFollowListRequest 客户跟进记录列表请求DTO
type CustomerFollowListRequest struct {
	CustomerID uint `form:"customer_id" binding:"required"` // 客户ID
	Page       int  `form:"page"`                           // 页码，默认1
	Count      int  `form:"count"`                          // 每页数量，默认10
}

// CustomerFollowItem 客户跟进记录项DTO
type CustomerFollowItem struct {
	ID         uint   `json:"id"`
	CustomerID uint   `json:"customer_id"`
	Stage      uint8  `json:"stage"`
	Priority   uint8  `json:"priority"`
	Progress   uint8  `json:"progress"`
	Content    string `json:"content"`
	CreatedBy  uint   `json:"created_by"`
	CreatedAt  string `json:"created_at"` // 格式化后的创建时间
}

// CustomerFollowListResponse 客户跟进记录列表响应DTO
type CustomerFollowListResponse struct {
	Data  []CustomerFollowItem `json:"data"`
	Size  int                  `json:"size"`
	IsEnd bool                 `json:"isEnd"`
}

// CreateCustomerFollowRequest 创建客户跟进记录请求DTO
type CreateCustomerFollowRequest struct {
	CustomerID uint   `form:"customer_id" binding:"required"` // 客户ID
	Stage      uint8  `form:"stage" binding:"required"`       // 跟进阶段
	Priority   uint8  `form:"priority"`                       // 优先级
	Progress   uint8  `form:"progress"`                       // 进度百分比
	Content    string `form:"content" binding:"required"`     // 跟进内容
}

// UpdateCustomerFollowRequest 更新客户跟进记录请求DTO
type UpdateCustomerFollowRequest struct {
	FollowID uint   `form:"follow_id" binding:"required"` // 跟进记录ID
	Stage    uint8  `form:"stage"`                        // 跟进阶段
	Priority uint8  `form:"priority"`                     // 优先级
	Progress uint8  `form:"progress"`                     // 进度百分比
	Content  string `form:"content"`                      // 跟进内容
}

// DeleteCustomerFollowRequest 删除客户跟进记录请求DTO
type DeleteCustomerFollowRequest struct {
	FollowID uint `form:"follow_id" binding:"required"` // 跟进记录ID
}

// CustomerStatisticsRequest 客户统计请求DTO
type CustomerStatisticsRequest struct {
	StartDate string `form:"start_date"` // 开始日期
	EndDate   string `form:"end_date"`   // 结束日期
}

// CustomerStatisticsItem 客户统计项DTO
type CustomerStatisticsItem struct {
	TotalCount     int `json:"total_count"`
	ActiveCount    int `json:"active_count"`
	InactiveCount  int `json:"inactive_count"`
	HighLevelCount int `json:"high_level_count"`
	MidLevelCount  int `json:"mid_level_count"`
	LowLevelCount  int `json:"low_level_count"`
}

// ImportCustomersRequest 导入客户请求DTO
type ImportCustomersRequest struct {
	Data []CreateCustomerRequest `json:"data" binding:"required"` // 客户数据
}

// ExportCustomersRequest 导出客户请求DTO
type ExportCustomersRequest struct {
	Format   string `form:"format"`   // 导出格式：excel, csv
	Status   *uint8 `form:"status"`   // 状态筛选
	Level    *uint8 `form:"level"`    // 等级筛选
	Region   string `form:"region"`   // 地区筛选
	Industry string `form:"industry"` // 行业筛选
	Source   string `form:"source"`   // 来源筛选
}

// BatchUpdateCustomersRequest 批量更新客户请求DTO
type BatchUpdateCustomersRequest struct {
	CustomerIDs []uint `json:"customer_ids" binding:"required"` // 客户ID列表
	Level       *uint8 `json:"level"`                           // 客户等级
	Status      *uint8 `json:"status"`                          // 状态
	Region      string `json:"region"`                          // 所在地区
	Industry    string `json:"industry"`                        // 所属行业
	Source      string `json:"source"`                          // 客户来源
}

// BatchDeleteCustomersRequest 批量删除客户请求DTO
type BatchDeleteCustomersRequest struct {
	CustomerIDs []uint `json:"customer_ids" binding:"required"` // 客户ID列表
}
