package tablet_control

import (
	"errors"
	"marketing-app/internal/handler/tablet_control/dto"
	"marketing-app/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TabletControl interface {
	QueryWarranty(c *gin.Context, barcode, number, imei string) (*model.Warranty, error)
	VerifyPhoneCodeFromDB(c *gin.Context, code, phone string, expireMinutes int) (int, error)
	GetWarrantyByImei(c *gin.Context, imei string) (*dto.WarrantyWithEndpoint, error)
	GetWarrantyByBarcode(c *gin.Context, barcode string) (*dto.WarrantyWithEndpoint, error)
	GetWarrantyByNumber(c *gin.Context, number string) (*dto.WarrantyWithEndpoint, error)
}

type tabletControl struct {
	db *gorm.DB
}

func NewTabletControl(db *gorm.DB) TabletControl {
	return &tabletControl{
		db: db,
	}
}

// QueryWarranty 查询保卡信息 - 优先匹配barcode，其次number，最后imei
func (t *tabletControl) QueryWarranty(c *gin.Context, barcode, number, imei string) (*model.Warranty, error) {
	var warranty model.Warranty
	var err error

	if barcode != "" {
		err = t.db.WithContext(c).Where("(barcode = ? OR ext_barcode = ?) AND status = 1", barcode, barcode).First(&warranty).Error
	} else if number != "" {
		err = t.db.WithContext(c).Where("number = ? AND status = 1", number).First(&warranty).Error
	} else if imei != "" {
		err = t.db.WithContext(c).Where("imei = ? AND status = 1", imei).First(&warranty).Error
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &warranty, nil
}

// VerifyPhoneCodeFromDB 从数据库验证手机验证码
// 返回值：1=成功，-1=验证码错误，-2=验证码错误，2=验证码已过期
func (t *tabletControl) VerifyPhoneCodeFromDB(c *gin.Context, code, phone string, expireMinutes int) (int, error) {
	var phoneCode model.PhoneCode
	err := t.db.WithContext(c).Where("phone = ? AND code = ?", phone, code).First(&phoneCode).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return -1, nil // 验证码不正确
		}
		return -2, err // 数据库错误
	}

	// 检查验证码是否已过期（30分钟）
	if time.Since(phoneCode.UpdatedAt).Minutes() > float64(expireMinutes) {
		return 2, nil // 验证码已过期
	}

	return 1, nil // 验证成功
}

// GetWarrantyByImei 通过IMEI查询warranty信息（包含endpoint LEFT JOIN）
func (t *tabletControl) GetWarrantyByImei(c *gin.Context, imei string) (*dto.WarrantyWithEndpoint, error) {
	var result dto.WarrantyWithEndpoint

	err := t.db.WithContext(c).
		Table("warranty w").
		Select(`w.*, 
			e.name as endpoint_name,
			e.address as endpoint_address,
			e.phone as endpoint_phone,
			e.manager as endpoint_manager`).
		Joins("LEFT JOIN endpoint e ON w.endpoint = e.id").
		Where("w.imei = ? AND w.status = 1", imei).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &result, nil
}

// GetWarrantyByBarcode 通过Barcode查询warranty信息（包含endpoint LEFT JOIN）
func (t *tabletControl) GetWarrantyByBarcode(c *gin.Context, barcode string) (*dto.WarrantyWithEndpoint, error) {
	var result dto.WarrantyWithEndpoint

	err := t.db.WithContext(c).
		Table("warranty w").
		Select(`w.*, 
			e.name as endpoint_name,
			e.address as endpoint_address,
			e.phone as endpoint_phone,
			e.manager as endpoint_manager`).
		Joins("LEFT JOIN endpoint e ON w.endpoint = e.id").
		Where("(w.barcode = ? OR w.ext_barcode = ?) AND w.status = 1", barcode, barcode).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &result, nil
}

// GetWarrantyByNumber 通过Number查询warranty信息（包含endpoint LEFT JOIN）
func (t *tabletControl) GetWarrantyByNumber(c *gin.Context, number string) (*dto.WarrantyWithEndpoint, error) {
	var result dto.WarrantyWithEndpoint

	err := t.db.WithContext(c).
		Table("warranty w").
		Select(`w.*, 
			e.name as endpoint_name,
			e.address as endpoint_address,
			e.phone as endpoint_phone,
			e.manager as endpoint_manager`).
		Joins("LEFT JOIN endpoint e ON w.endpoint = e.id").
		Where("w.number = ? AND w.status = 1", number).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &result, nil
}
