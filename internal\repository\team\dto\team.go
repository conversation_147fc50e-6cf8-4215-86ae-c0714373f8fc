package dto

// TeamMember 团队成员数据
type TeamMember struct {
	ID            uint    `json:"id"`
	Username      string  `json:"username"`
	Avatar        *string `json:"avatar,omitempty"`
	Name          *string `json:"name,omitempty"`
	ExtendName    *string `json:"extend_name,omitempty"`
	ExtendPhone   *string `json:"extend_phone,omitempty"`
	WechatID      *string `json:"wechat_id,omitempty"`
	WechatImage   *string `json:"wechat_image,omitempty"`
	QrCodeAddress *string `json:"qr_code_address,omitempty"`
	Role          string  `json:"role"`
	Status        int8    `json:"status"`
}
