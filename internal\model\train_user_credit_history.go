package model

import (
	"time"
)

type TrainUserCreditHistory struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint      `gorm:"type:int(10) unsigned;not null" json:"user_id"`
	Credit    float64   `gorm:"type:decimal(5,2);not null;comment:学分变化值" json:"credit"`
	Type      uint8     `gorm:"type:tinyint(3) unsigned;not null;comment:1-资料学习，2-课程学习" json:"type"`
	SourceID  uint      `gorm:"type:int(10) unsigned;not null;comment:学分来源id" json:"source_id"`
	CreatedAt time.Time `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (TrainUserCreditHistory) TableName() string {
	return "train_user_credit_history"
}
