package dto

import "marketing-app/internal/repository/endpoint/endpoint/dto"

// EndpointsRequest 终端列表查询请求
type EndpointsRequest struct {
	Type     int     `form:"type" json:"type"`         // 终端类型：1-售前，2-售后
	City     string  `form:"city" json:"city"`         // 城市
	District string  `form:"district" json:"district"` // 区域
	Channel  string  `form:"channel" json:"channel"`   // 渠道
	Lng      float64 `form:"lng" json:"lng"`           // 经度
	Lat      float64 `form:"lat" json:"lat"`           // 纬度
	Count    int     `form:"count" json:"count"`       // 页展示量
	Page     int     `form:"page" json:"page"`         // 页码
}

type EndpointsResponse struct {
	Data []dto.EndpointItem `json:"data"`
}

// EndpointOptionsResponse 终端选项响应
type EndpointOptionsResponse struct {
	ChannelLevel     []OptionItem `json:"channel_level"`
	PositionPriority []string     `json:"position_priority"`
	CompetitorBrand  []string     `json:"competitor_brand"`
	ChannelType      []OptionItem `json:"channel_type"`
}

type OptionItem struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// EndpointImageRequest 终端形象请求
type EndpointImageRequest struct {
	Endpoint int `form:"endpoint" binding:"required" json:"endpoint"`
}

// EndpointImageResponse 终端形象响应
type EndpointImageResponse struct {
	Name             string       `json:"name"`
	Manager          string       `json:"manager"`
	Phone            string       `json:"phone"`
	Images           []string     `json:"images"`
	Lng              float64      `json:"lng"`
	Lat              float64      `json:"lat"`
	Code             string       `json:"code"`
	Endpoint         int          `json:"endpoint"`
	ChannelLevel     int          `json:"channel_level"`
	ChannelType      int          `json:"channel_type"`
	PositionPriority string       `json:"position_priority"`
	Area             string       `json:"area"`
	Sales            string       `json:"sales"`
	CreatedAt        string       `json:"created_at"`
	Competitors      []Competitor `json:"competitors"`
}

type Competitor struct {
	ID               int      `json:"id"`
	Brand            string   `json:"brand"`
	PositionPriority string   `json:"position_priority"`
	Pics             []string `json:"pics"`
}

// EndpointNewImageRequest 终端形象变更请求
type EndpointNewImageRequest struct {
	Endpoint int `form:"endpoint" json:"endpoint"`
}

// EndpointNewImageResponse 终端形象变更响应
type EndpointNewImageResponse struct {
	ID                 int          `json:"id"`
	Name               string       `json:"name"`
	Manager            string       `json:"manager"`
	Phone              string       `json:"phone"`
	Images             []string     `json:"images"`
	Lng                float64      `json:"lng"`
	Lat                float64      `json:"lat"`
	PositionPriority   string       `json:"position_priority"`
	Area               string       `json:"area"`
	Sales              string       `json:"sales"`
	ChannelLevel       int          `json:"channel_level"`
	ChannelType        int          `json:"channel_type"`
	Status             int          `json:"status"`
	TopAgencyOpinion   *string      `json:"top_agency_opinion"`
	ManagerOpinion     *string      `json:"manager_opinion"`
	CreatedAt          *string      `json:"created_at"`
	UpdatedAt          *string      `json:"updated_at"`
	TopAgencyAuditedAt *string      `json:"top_agency_audited_at"`
	ManagerAuditedAt   *string      `json:"manager_audited_at"`
	Competitors        []Competitor `json:"competitors"`
}

// EndpointNewImagePostRequest 终端形象变更POST请求
type EndpointNewImagePostRequest struct {
	ID               *int         `json:"id"`
	Endpoint         *int         `json:"endpoint"`
	Lng              string       `json:"lng"` // 高德坐标经度，接收字符串格式
	Lat              string       `json:"lat"` // 高德坐标纬度，接收字符串格式
	PositionPriority string       `json:"position_priority"`
	Area             int          `json:"area"`
	Sales            int          `json:"sales"`
	ChannelLevel     int          `json:"channel_level"`
	ChannelType      int          `json:"channel_type"`
	Images           []string     `json:"images"`
	Competitors      []Competitor `json:"competitors"`
}

// EndpointNoticeRequest 终端公告请求
type EndpointNoticeRequest struct {
	Ts    int `form:"ts" json:"ts"`       // 时间戳
	Count int `form:"count" json:"count"` // 数量
}

// EndpointNoticeResponse 终端公告响应
type EndpointNoticeResponse struct {
	Data  []NoticeItem `json:"data"`
	Size  int          `json:"size"`
	IsEnd bool         `json:"isEnd"`
}

type NoticeItem struct {
	ID        int    `json:"id"`
	Content   string `json:"content"`
	Title     string `json:"title"`
	Author    string `json:"author"`
	CreatedAt string `json:"created_at"`
}

// EndpointNoticeByIdRequest 根据ID获取终端公告请求
type EndpointNoticeByIdRequest struct {
	NoticeID string `form:"notice_id" binding:"required" json:"notice_id"`
}

// EndpointInfoByUsernameRequest 根据用户名获取终端信息请求
type EndpointInfoByUsernameRequest struct {
	Username string `form:"username" binding:"required" json:"username"`
}

// EndpointInfoByUsernameResponse 根据用户名获取终端信息响应
type EndpointInfoByUsernameResponse struct {
	ID           int    `json:"id"`
	Name         string `json:"name"`
	TopAgency    string `json:"top_agency"`
	SecondAgency string `json:"second_agency"`
	Address      string `json:"address"`
	Manager      string `json:"manager"`
	Phone        string `json:"phone"`
}

// EndpointRegionRequest 终端区域请求
type EndpointRegionRequest struct {
	UID int `form:"uid" json:"uid"`
}

// EndpointRegionResponse 终端区域响应
type EndpointRegionResponse struct {
	Data []RegionItem `json:"data"`
}

type RegionItem struct {
	ID    int    `json:"id"`
	PID   int    `json:"pid"`
	Name  string `json:"name"`
	Level int    `json:"level"`
}

// EndpointStatRequest 终端统计请求
type EndpointStatRequest struct {
	TopAgency    string `form:"top_agency" json:"top_agency"`
	SecondAgency string `form:"second_agency" json:"second_agency"`
}

// EndpointStatResponse 终端统计响应
type EndpointStatResponse struct {
	Data []StatItem `json:"data"`
}

type StatItem struct {
	Type  string `json:"type"`
	Total int    `json:"total"`
}

// EndpointDetailRequest 终端详情请求
type EndpointDetailRequest struct {
	Uid          int    `form:"uid" json:"uid"`
	TopAgency    string `form:"top_agency" json:"top_agency"`
	SecondAgency string `form:"second_agency" json:"second_agency"`
	Page         int    `form:"page" json:"page"`
	Count        int    `form:"count" json:"count"`
}

// EndpointDetailResponse 终端详情响应
type EndpointDetailResponse struct {
	IsEnd  bool         `json:"isEnd"`
	Detail []DetailItem `json:"detail"`
}

type DetailItem struct {
	Name   string `json:"name"`
	Type   string `json:"type"`
	Belong string `json:"belong"`
}

// GetEndpointInfoRequest 获取终端信息请求
type GetEndpointInfoRequest struct {
	Endpoint string `form:"endpoint" binding:"required" json:"endpoint"`
}

// GetEndpointInfoResponse 获取终端信息响应
type GetEndpointInfoResponse struct {
	TopAgency       int    `json:"top_agency"`
	SecondAgency    int    `json:"second_agency"`
	IsDirectSales   int    `json:"is_direct_sales"`
	Compound        int    `json:"compound"`
	EndpointChannel string `json:"endpoint_channel"`
	Status          int    `json:"status"`
}

// EndpointListRequest 终端列表请求
type EndpointListRequest struct {
	TopAgency    int `form:"top_agency" binding:"required" json:"top_agency"`
	SecondAgency int `form:"second_agency" json:"second_agency"`
}

// EndpointListResponse 终端列表响应
type EndpointListResponse struct {
	Data []EndpointListItem `json:"data"`
}

type EndpointListItem struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// SalesStatRequest 销售统计请求
type SalesStatRequest struct {
	AccessToken string `form:"access_token" json:"access_token" binding:"required"` // API访问令牌
}

// SalesStatResponse 销售统计响应
type SalesStatResponse struct {
	Data                     interface{} `json:"data"`                       // 销售统计数据
	StatisticsInstructionURL string      `json:"statistics_instruction_url"` // 统计说明页面URL
	AssessmentInstructionURL string      `json:"assessment_instruction_url"` // 评估说明页面URL
	StatisticsURL            string      `json:"statistics_url"`             // 统计页面URL
	AssessmentURL            string      `json:"assessment_url"`             // 评估页面URL
	TerminalURL              string      `json:"terminal_url"`               // 终端页面URL
}
