package model

import (
	"time"
)

type Train struct {
	ID                 uint       `gorm:"primaryKey;autoIncrement;comment:主键" json:"id"`
	Name               string     `gorm:"type:varchar(50);not null;default:'';comment:名称" json:"name"`
	Description        string     `gorm:"type:text;not null;comment:描述" json:"description"`
	Preview            string     `gorm:"type:text;not null;comment:预览图" json:"preview"`
	Path               string     `gorm:"type:text;not null;comment:下载路径" json:"path"`
	ArticleLink        *string    `gorm:"type:varchar(500);comment:文章链接地址" json:"article_link"`
	Type               uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:大分类" json:"type"`
	Category           uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:小分类" json:"category"`
	Status             uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:状态，1为通过，0为未通过" json:"status"`
	Top                uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否置顶，1为置顶，0为未置顶" json:"top"`
	Share              uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否可以分享，1为可以分享" json:"share"`
	Star               uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否重要，1为重要，0为未重要" json:"star"`
	DownloadCount      uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:下载量" json:"download_count"`
	Increment          uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:下载增量" json:"increment"`
	CreatedAt          time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"type:timestamp;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
	Recommend          uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否加入推荐" json:"recommend"`
	DailyCount         uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:日点击量" json:"daily_count"`
	MonthCount         uint       `gorm:"type:int(11) unsigned;not null;default:0;comment:30天点击量" json:"month_count"`
	LastUpdatedAt      *time.Time `gorm:"type:timestamp;comment:每日最后更新时间" json:"last_updated_at"`
	MonthText          string     `gorm:"type:text;not null;comment:30天内每天的点击记录，json" json:"month_text"`
	Banner             uint8      `gorm:"type:tinyint(3) unsigned;not null;default:0;comment:是否作为banner" json:"banner"`
	BannerImage        string     `gorm:"type:varchar(100);not null;default:'';comment:banner图片" json:"banner_image"`
	Credit             float64    `gorm:"type:decimal(5,2) unsigned;not null;default:0.00;comment:学分" json:"credit"`
	CreditLearningTime uint16     `gorm:"type:smallint(5) unsigned;not null;default:0;comment:获得学分所需学习时长，秒" json:"credit_learning_time"`
	CreatedBy          uint       `gorm:"type:int(10) unsigned;not null;default:0;comment:创建人" json:"created_by"`
}

func (Train) TableName() string {
	return "train"
}
