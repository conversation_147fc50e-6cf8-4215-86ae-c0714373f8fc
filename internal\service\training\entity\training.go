package entity

type TrainVideosRequest struct {
	Ts    int64 `form:"ts"`    // 时间戳
	Count int   `form:"count"` // 数量限制
}

type TrainListRequest struct {
	Page     int  `form:"page"`     // 页码，默认1
	Count    int  `form:"count"`    // 每页数量，默认10
	Type     int  `form:"type"`     // 培训类型，-1表示所有类型
	Category int  `form:"category"` // 培训分类，-1表示所有分类
	Order    int  `form:"order"`    // 排序方式，1=按id倒序，2=按月下载量倒序
	UID      uint // 用户ID，从上下文获取
}

type TrainCategoriesRequest struct {
	Type int `form:"type"` // 培训类型，0或空表示所有类型
}

type AddLearningHistoryRequest struct {
	ID           uint   // 培训ID
	LearningTime string // 学习时长
	UID          uint   // 用户ID，从上下文获取
}

type AddShareHistoryRequest struct {
	TrainID uint // 培训ID
	UID     uint // 用户ID，从上下文获取
}

type TrainSearchRequest struct {
	Keyword string // 搜索关键词
	UID     uint   // 用户ID，从上下文获取
}

type TrainHotRequest struct {
	Page  int // 页码，默认1
	Count int // 每页数量，默认10
}

type AddCollectionRequest struct {
	TrainID uint // 培训ID
	UID     uint // 用户ID，从上下文获取
}

type CancelCollectionRequest struct {
	TrainID uint // 培训ID
	UID     uint // 用户ID，从上下文获取
}

type CollectionListRequest struct {
	Page  int  // 页码，默认1
	Count int  // 每页数量，默认10
	UID   uint // 用户ID，从上下文获取
}

type TrainBannerRequest struct {
	Type int  // 培训类型，不能为空
	UID  uint // 用户ID，从上下文获取
}
