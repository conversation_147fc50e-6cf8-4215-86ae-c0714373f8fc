package model

import (
	"time"
)

// EndpointNotice 终端公告表模型
// 注意：这个是给APP使用的告示,数据库里的notice表是给后台使用的告示表
type EndpointNotice struct {
	ID        uint       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Content   string     `gorm:"type:text;column:content" json:"content"`                // 通知内容,是富文本内容
	Title     string     `gorm:"type:varchar(255);not null;column:title" json:"title"`   // 告示标题
	Author    string     `gorm:"type:varchar(255);not null;column:author" json:"author"` // 告示撰写人
	CreatedAt *time.Time `gorm:"column:created_at" json:"created_at"`                    // 创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at" json:"updated_at"`                    // 更新时间
}

// TableName 指定表名
func (EndpointNotice) TableName() string {
	return "endpoint_notice"
}
