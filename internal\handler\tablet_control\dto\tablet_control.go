package dto

import "marketing-app/internal/model"

// AllowUnknownAppRequest AllowUnknownApp函数POST请求
type AllowUnknownAppRequest struct {
	Barcode    string `form:"barcode" json:"barcode"`                            // 条码（可选，三选一）
	Number     string `form:"number" json:"number"`                              // 序列号（可选，三选一）
	IMEI       string `form:"imei" json:"imei"`                                  // IMEI码（可选，三选一）
	EndpointID int    `form:"endpoint_id" json:"endpoint_id" binding:"required"` // 终端ID（必填）
	Status     int    `form:"status" json:"status"`                              // 状态：0=禁止，1=允许（必填）
}

// VerifyPhoneCodeForAllowUnknownAppRequest AllowUnknownApp函数GET请求（验证手机验证码）
type VerifyPhoneCodeForAllowUnknownAppRequest struct {
	Phone string `form:"phone" binding:"required"` // 手机号
	Code  string `form:"code" binding:"required"`  // 验证码
}

// AllowUnknownAppResponse AllowUnknownApp函数通用响应
type AllowUnknownAppResponse struct {
	Success bool   `json:"success"` // 操作是否成功
	Message string `json:"message"` // 响应消息
}

// GetAllowNsfwUrlRequest GetAllowNsfwUrl函数GET请求
type GetAllowNsfwUrlRequest struct {
	Barcode string `form:"barcode" json:"barcode"` // 条码（可选，三选一）
	Number  string `form:"number" json:"number"`   // 序列号（可选，三选一）
	IMEI    string `form:"imei" json:"imei"`       // IMEI码（可选，三选一）
}

// GetAllowNsfwUrlResponse GetAllowNsfwUrl函数响应
type GetAllowNsfwUrlResponse struct {
	Data interface{} `json:"data"` // 响应数据
}

// SetAllowNsfwUrlRequest SetAllowNsfwUrl函数POST请求
type SetAllowNsfwUrlRequest struct {
	Barcode    string `form:"barcode" json:"barcode"`                            // 条码（可选，三选一）
	Number     string `form:"number" json:"number"`                              // 序列号（可选，三选一）
	IMEI       string `form:"imei" json:"imei"`                                  // IMEI码（可选，三选一）
	EndpointID int    `form:"endpoint_id" json:"endpoint_id" binding:"required"` // 终端ID（必填）
	Status     int    `form:"status" json:"status"`                              // 状态值（必填）
}

// CancelBindingsRequest CancelBindings函数请求
type CancelBindingsRequest struct {
	Barcode string `form:"barcode" json:"barcode"` // 条码（可选，三选一）
	Number  string `form:"number" json:"number"`   // 序列号（可选，三选一）
	IMEI    string `form:"imei" json:"imei"`       // IMEI码（可选，三选一）
}

// CancelBindingsResponse CancelBindings函数响应
type CancelBindingsResponse struct {
	Success bool   `json:"success"` // 操作是否成功
	Message string `json:"message"` // 响应消息
}

// WarrantyWithEndpoint 包含endpoint信息的warranty结构体
type WarrantyWithEndpoint struct {
	model.Warranty
	// endpoint表的字段
	EndpointName    *string `json:"endpoint_name"`
	EndpointAddress *string `json:"endpoint_address"`
	EndpointPhone   *string `json:"endpoint_phone"`
	EndpointManager *string `json:"endpoint_manager"`
}

// UnbindWatchRequest UnbindWatch函数POST请求
type UnbindWatchRequest struct {
	Barcode  string `form:"barcode" json:"barcode"`   // 条码（可选，三选一）
	Number   string `form:"number" json:"number"`     // 序列号（可选，三选一）
	IMEI     string `form:"imei" json:"imei"`         // IMEI码（可选，三选一）
	Username string `form:"username" json:"username"` // 用户名（可选）
}

// UnbindWatchResponse UnbindWatch函数响应
type UnbindWatchResponse struct {
	Success bool   `json:"success"` // 操作是否成功
	Message string `json:"message"` // 响应消息
}
