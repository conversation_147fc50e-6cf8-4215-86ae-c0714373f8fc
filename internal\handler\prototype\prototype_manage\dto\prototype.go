package dto

type CreatePrototypeResp struct {
	Message string `json:"message"`
}

type PrototypeListResp struct {
	Data          []map[string]interface{} `json:"data"`
	Size          int                      `json:"size"`
	IsEnd         bool                     `json:"isEnd"`
	ModelCategory []map[string]interface{} `json:"model_category"`
}

type DeletePrototypeResp struct {
	Message string `json:"message"`
}

type CheckPrototypeResp struct {
	Model   string `json:"model"`
	Barcode string `json:"barcode"`
}

// PrototypeLimitResponse 代理商样机限制响应
type PrototypeLimitResponse struct {
	PrototypeLimit          int   `json:"prototype_limit"`           // 样机数量限制
	PrototypeFrequencyLimit int   `json:"prototype_frequency_limit"` // 样机频次限制
	PrototypeTotal          int64 `json:"prototype_total"`           // 当前总样机数
	PrototypeMonthTotal     int64 `json:"prototype_month_total"`     // 当前月样机数
}

// PrototypeUserListResponse 代理商演示用户列表响应
type PrototypeUserListResponse struct {
	UserList  []PrototypeUserInfo `json:"user_list"`  // 用户列表
	UserLimit int                 `json:"user_limit"` // 用户限制（-1表示无限制）
}

// PrototypeUserInfo 演示用户信息
type PrototypeUserInfo struct {
	ID    int    `json:"id"`    // 用户ID
	Phone string `json:"phone"` // 手机号
}

// PrototypeUserBindRequest 终端演示用户绑定请求
type PrototypeUserBindRequest struct {
	Phone      string `json:"phone" binding:"required"`       // 手机号
	Code       string `json:"code" binding:"required"`        // 验证码
	EndpointID int    `json:"endpoint_id" binding:"required"` // 终端ID
}

// PrototypeUserBindResponse 终端演示用户绑定响应
type PrototypeUserBindResponse struct {
	ID    int    `json:"id"`    // 用户ID
	Phone string `json:"phone"` // 手机号
}

// PrototypeUserChangeRequest 终端演示用户修改手机号请求
type PrototypeUserChangeRequest struct {
	EndpointID int    `json:"endpoint_id" binding:"required"` // 终端ID
	Code       string `json:"code" binding:"required"`        // 验证码
	OldPhone   string `json:"old_phone" binding:"required"`   // 旧手机号
	NewPhone   string `json:"new_phone" binding:"required"`   // 新手机号
}

// PrototypeUserChangeResponse 终端演示用户修改手机号响应
type PrototypeUserChangeResponse struct {
	Success bool `json:"success"` // 是否成功
}

// PrototypeUserDeleteRequest 终端演示用户删除请求
type PrototypeUserDeleteRequest struct {
	EndpointID int    `json:"endpoint_id" binding:"required"` // 终端ID
	Phone      string `json:"phone" binding:"required"`       // 手机号
}

// PrototypeUserDeleteResponse 终端演示用户删除响应
type PrototypeUserDeleteResponse struct {
	Success bool `json:"success"` // 是否成功
}

// PrototypeWarrantyQueryRequest PrototypeWarrantyQuery函数GET请求
type PrototypeWarrantyQueryRequest struct {
	Barcode string `form:"barcode" json:"barcode"` // 条码（可选，与number至少填一个）
	Number  string `form:"number" json:"number"`   // 序列号（可选，与barcode至少填一个）
	IMEI    string `form:"imei" json:"imei"`       // IMEI码（可选）
}

// PrototypeWarrantyQueryResponse PrototypeWarrantyQuery函数响应
type PrototypeWarrantyQueryResponse struct {
	Data interface{} `json:"data"` // 响应数据，包含warranty、has_warranty、has_prototype、prototype_data
}
