package model

import (
	"time"
)

type TrainShareHistory struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint      `gorm:"type:int(10) unsigned;not null;comment:用户ID" json:"user_id"`
	TrainID   uint      `gorm:"type:int(10) unsigned;not null;comment:培训ID" json:"train_id"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

func (TrainShareHistory) TableName() string {
	return "train_share_history"
}
