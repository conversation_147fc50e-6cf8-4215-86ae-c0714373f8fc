package dto

import (
	"database/sql/driver"
	"time"
)

type TrainVideoItem struct {
	ID          uint      `json:"id"`
	Title       string    `json:"title"`
	Preview     string    `json:"preview"`
	Path        string    `json:"path"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type TrainVideosResponse struct {
	Data  []TrainVideoItem `json:"data"`
	Size  int              `json:"size"`
	IsEnd bool             `json:"isEnd"`
}

// NullTime represents a nullable time.Time
type NullTime struct {
	Time  time.Time
	Valid bool
}

func (nt *NullTime) Scan(value interface{}) error {
	if value == nil {
		nt.Valid = false
		return nil
	}
	nt.Valid = true
	switch v := value.(type) {
	case time.Time:
		nt.Time = v
	default:
		nt.Valid = false
	}
	return nil
}

func (nt NullTime) Value() (driver.Value, error) {
	if !nt.Valid {
		return nil, nil
	}
	return nt.Time, nil
}

type TrainItem struct {
	ID                 uint      `json:"id"`
	Name               string    `json:"name"`
	Preview            string    `json:"preview"`
	Path               string    `json:"path"`
	ArticleLink        *string   `json:"article_link"`
	Share              uint8     `json:"share"`
	Description        string    `json:"description"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	Star               uint8     `json:"star"`
	DownloadCount      uint      `json:"download_count"`
	Credit             float64   `json:"credit"`
	CreditLearningTime uint16    `json:"credit_learning_time"`
	Collection         uint8     `json:"collection"`          // 是否收藏：1=已收藏，0=未收藏
	GetCredit          uint8     `json:"get_credit"`          // 是否已获得学分：1=已获得，0=未获得
	CollectionCount    uint      `json:"collection_count"`    // 收藏总数
	ReadTime           *NullTime `json:"read_time,omitempty"` // 最后阅读时间
	ShareURL           string    `json:"share_url"`           // 分享URL
}

type TrainListResponse struct {
	Data  []TrainItem `json:"data"`
	Size  int         `json:"size"`
	IsEnd bool        `json:"isEnd"`
}

type TrainTypeItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}

type TrainCategoryItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}

type TrainTypeCategoryItem struct {
	Type     uint   `json:"type"`
	Category uint   `json:"category"`
	Title    string `json:"title"`
}

type TrainSearchItem struct {
	ID                 uint      `json:"id"`
	Name               string    `json:"name"`
	Preview            string    `json:"preview"`              // 预览图JSON字符串，待service层处理
	Path               string    `json:"path"`                 // 视频路径
	ArticleLink        *string   `json:"article_link"`         // 文章链接
	Share              uint8     `json:"share"`                // 是否可分享
	Description        string    `json:"description"`          // 描述
	CreatedAt          time.Time `json:"created_at"`           // 创建时间
	UpdatedAt          time.Time `json:"updated_at"`           // 更新时间
	Star               uint8     `json:"star"`                 // 是否重要
	DownloadCount      uint      `json:"download_count"`       // 下载次数
	Credit             float64   `json:"credit"`               // 学分
	CreditLearningTime uint16    `json:"credit_learning_time"` // 获得学分所需学习时长
	GetCredit          uint8     `json:"get_credit"`           // 是否已获得学分：1=已获得，0=未获得
	ShareURL           string    `json:"share_url"`            // 分享URL（待service层处理）
}

type TrainHotItem struct {
	ID            uint      `json:"id"`
	Name          string    `json:"name"`
	Preview       string    `json:"preview"`        // 预览图JSON字符串，待service层处理
	Path          string    `json:"path"`           // 视频路径
	Description   string    `json:"description"`    // 描述
	ArticleLink   *string   `json:"article_link"`   // 文章链接
	Share         uint8     `json:"share"`          // 是否可分享
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`     // 更新时间
	Star          uint8     `json:"star"`           // 是否重要
	DownloadCount uint      `json:"download_count"` // 下载次数
	ShareURL      string    `json:"share_url"`      // 分享URL（待service层处理）
}

type TrainHotResponse struct {
	Data  []TrainHotItem `json:"data"`
	Size  int            `json:"size"`
	IsEnd bool           `json:"isEnd"`
}

type CollectionListItem struct {
	ID              uint      `json:"id"`
	Name            string    `json:"name"`
	Preview         string    `json:"preview"`             // 预览图JSON字符串，待service层处理
	Path            string    `json:"path"`                // 视频路径
	ArticleLink     *string   `json:"article_link"`        // 文章链接
	Share           uint8     `json:"share"`               // 是否可分享
	Description     string    `json:"description"`         // 描述
	CreatedAt       time.Time `json:"created_at"`          // 创建时间
	UpdatedAt       time.Time `json:"updated_at"`          // 更新时间
	Star            uint8     `json:"star"`                // 是否重要
	DownloadCount   uint      `json:"download_count"`      // 下载次数
	Collection      uint8     `json:"collection"`          // 收藏状态：1=已收藏，0=未收藏
	CollectionCount uint      `json:"collection_count"`    // 收藏总数
	ReadTime        *NullTime `json:"read_time,omitempty"` // 最后阅读时间
	ShareURL        string    `json:"share_url"`           // 分享URL（待service层处理）
}

type CollectionListResponse struct {
	Data  []CollectionListItem `json:"data"`
	Size  int                  `json:"size"`
	IsEnd bool                 `json:"isEnd"`
}

type TrainBannerItem struct {
	ID            uint      `json:"id"`
	Name          string    `json:"name"`
	Preview       string    `json:"preview"`        // 预览图JSON字符串，待service层处理
	Path          string    `json:"path"`           // 视频路径
	ArticleLink   *string   `json:"article_link"`   // 文章链接
	Share         uint8     `json:"share"`          // 是否可分享
	Description   string    `json:"description"`    // 描述
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`     // 更新时间
	Star          uint8     `json:"star"`           // 是否重要
	DownloadCount uint      `json:"download_count"` // 下载次数
	BannerImage   string    `json:"banner_image"`   // Banner图片
	Collection    uint8     `json:"collection"`     // 收藏状态：1=已收藏，0=未收藏
	ShareURL      string    `json:"share_url"`      // 分享URL（待service层处理）
}
