package warranty_homepage

import (
	"github.com/gin-gonic/gin"
	"marketing-app/internal/handler/warranty/warranty_homepage"
	baseHandler "marketing-app/internal/handler/warranty/warranty_homepage"
	"marketing-app/internal/pkg/db"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	baseRepo "marketing-app/internal/repository/warranty/base"
	baseService "marketing-app/internal/service/warranty/warranty_homepage"
)

type WarrantyBaseRouter struct {
	warrantyHandler warranty_homepage.WarrantyBaseHandler
}

func NewWarrantyRouter() *WarrantyBaseRouter {
	database, _ := db.GetDB()
	warrantyBaseRepo := baseRepo.NewWarranty(database)
	ueRepo := userEndpointRepo.NewUserEndpoint(database)
	warrantyBaseSvc := baseService.NewWarrantyBase(warrantyBaseRepo, ueRepo)
	return &WarrantyBaseRouter{
		warrantyHandler: baseHandler.NewWarrantyHandler(warrantyBaseSvc),
	}
}

func (w *WarrantyBaseRouter) Register(r *gin.RouterGroup) {
	g := r.Group("warranty_homepage")
	{
		g.GET("/list", w.warrantyHandler.ListWarranties) // 首页展示保卡列表
		g.GET("/warranty/", w.warrantyHandler.GetWarrantyCardByIdentifier)
	}
}
